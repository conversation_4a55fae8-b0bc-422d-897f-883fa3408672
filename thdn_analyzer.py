#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THDN分析器
"""

import numpy as np
from concurrent.futures import ProcessPoolExecutor
# import time
from scipy.signal.windows import blackmanharris
from freq_split_optimized import split_freq_steps_optimized


def calculate_amplitude_spectrum(audio_segment, fft_size, sr):
    """
    计算音频段的幅度谱

    Args:
        audio_segment: 音频段数据
        fft_size: FFT长度
        sr: 采样率

    Returns:
        tuple: (frequencies, amplitude_spectrum)
               frequencies: 频率数组
               amplitude_spectrum: 幅度谱（已修正rfft）
    """
    # 应用窗函数和FFT
    window = blackmanharris(fft_size)
    windowed = audio_segment * window

    # FFT计算
    fft_result = np.fft.rfft(windowed) / window.sum()
    frequencies = np.fft.rfftfreq(fft_size, 1/sr)

    # 计算幅度谱
    amplitude_spectrum = np.abs(fft_result)
    # 对于rfft，除了DC分量(index=0)和Nyquist分量(最后一个)，其他分量需要乘以2
    amplitude_spectrum[1:-1] *= 2.0

    return frequencies, amplitude_spectrum


def get_bandwidth_strategy(frequency):
    """
    根据频率确定带宽策略
    <100Hz: 使用百分比带宽（扩大范围）
    100-200Hz: 使用百分比带宽
    200-20000Hz: 使用固定200Hz带宽
    """
    if frequency < 100:
        # 极低频段使用较大的百分比带宽
        percent = 40.0
        return 'percent', percent
    elif 100 <= frequency < 200:
        # 低频段使用百分比带宽
        percent = 30.0
        return 'percent', percent
    elif 200 <= frequency:
        # 中高频段使用固定200Hz带宽
        return 'fixed', 200.0
    else:
        # 默认情况（不应该到达这里）
        return 'fixed', 200.0

def analyze_segment_thdn_only(args):
    """
    分析单个频段THDN+N，只使用待测音频
    """
    seg_idx, test_start_time, test_end_time, expected_freq, test_audio, sr = args

    try:
        # print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz")

        # 提取待测音频段
        test_start_sample = int(test_start_time * sr)
        test_end_sample = int(test_end_time * sr)

        # 确保索引在有效范围内
        test_start_sample = max(0, min(test_start_sample, len(test_audio)))
        test_end_sample = max(0, min(test_end_sample, len(test_audio)))

        test_total_segment = test_audio[test_start_sample:test_end_sample]

        # 检查音频段长度
        if len(test_total_segment) == 0:
            print(f"!!! 音频段为空")
            return False, None

        # 去掉开头和结尾各8%，保留中间84%
        trim_ratio = 0.08
        test_total_length = len(test_total_segment)
        test_trim_samples = int(test_total_length * trim_ratio)
        test_start_idx = test_trim_samples
        test_end_idx = test_total_length - test_trim_samples
        test_segment = test_total_segment[test_start_idx:test_end_idx]

        # 获取信号长度
        test_length = len(test_segment)

        # FFT长度根据频率范围自适应调整（使用adaptive_fundamental_removal.py的参数）
        if expected_freq < 1000:
            # 低频段（<1000Hz）：需要较高的频率分辨率
            fft_size = 8192  # 频率分辨率 ≈ 5.86 Hz
        else:
            # 高频段（>=1000Hz）：可以使用较低的频率分辨率
            fft_size = 4096   # 频率分辨率 ≈ 11.72 Hz

        # 如果信号长度小于FFT长度，进行零填充
        if test_length < fft_size:
            test_segment_padded = np.zeros(fft_size)
            start_idx = (fft_size - test_length) // 2
            test_segment_padded[start_idx:start_idx + test_length] = test_segment
            test_segment = test_segment_padded
        elif test_length > fft_size:
            start_idx = (test_length - fft_size) // 2
            test_segment = test_segment[start_idx:start_idx + fft_size]

        # 计算幅度谱
        display_freqs, test_amp_spectrum = calculate_amplitude_spectrum(test_segment, fft_size, sr)

        # 计算频率分辨率
        freq_resolution = sr / fft_size

        # print(f"    FFT设置: 长度={fft_size}, 分辨率={freq_resolution:.2f}Hz")
        # print(f"    FFT结果: 长度={len(test_amp_spectrum)}, 频率范围={display_freqs[0]:.1f}-{display_freqs[-1]:.1f}Hz")

        # 找待测信号的主频峰值（使用adaptive_fundamental_removal.py的搜索带宽策略）
        # 根据频率和FFT分辨率调整搜索带宽
        if expected_freq < 150:
            search_bandwidth = max(20.0, freq_resolution * 10)
        elif expected_freq < 500:
            search_bandwidth = max(15.0, freq_resolution * 8)
        elif expected_freq < 2000:
            search_bandwidth = max(10.0, freq_resolution * 6)
        else:
            search_bandwidth = max(8.0, freq_resolution * 5)

        # print(f"    搜索设置: 带宽={search_bandwidth:.1f}Hz, 范围={expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz")
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)

        # 找待测信号的峰值（使用幅度谱）
        if np.any(search_mask):
            test_search_amps = test_amp_spectrum[search_mask]
            if len(test_search_amps) == 0:
                # print(f"    ❌ 待测信号搜索范围内无数据")
                return False, None
            test_max_amp_idx = np.argmax(test_search_amps)
            test_search_indices = np.where(search_mask)[0]
            test_peak_idx = test_search_indices[test_max_amp_idx]
            test_fundamental_freq = display_freqs[test_peak_idx]
            test_peak_amp = test_amp_spectrum[test_peak_idx]  # 这就是检测值，替代缩放系数
        else:
            # print(f"    ❌ 待测信号搜索范围{expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz无数据")
            return False, None

        # 计算幅值dB
        peak_amp_db = 20 * np.log10(test_peak_amp) if test_peak_amp > 0 else -120
        # print(f"    待测峰值: {test_fundamental_freq:.1f}Hz, 幅值={peak_amp_db:.1f} dB")

        # 计算功率谱用于THDN+N计算（从100Hz开始）
        # 使用已经修正的幅度谱计算功率谱
        test_power_spectrum = test_amp_spectrum**2

        # 找到100Hz对应的bin
        freq_100hz_bin = int(100 / freq_resolution)

        # 从100Hz开始计算总功率
        total_power_freq = np.sum(test_power_spectrum[freq_100hz_bin:])

        # 混合带宽策略排除法计算THDN+N
        # 根据频率确定带宽策略
        bandwidth_type, bandwidth_value = get_bandwidth_strategy(test_fundamental_freq)

        # 计算主频带宽范围
        if bandwidth_type == 'percent':
            bandwidth_hz = test_fundamental_freq * bandwidth_value / 100.0
            fundamental_bandwidth_percent = bandwidth_value
        else:
            bandwidth_hz = bandwidth_value
            fundamental_bandwidth_percent = (bandwidth_value / test_fundamental_freq) * 100.0

        fundamental_freq_start = test_fundamental_freq - bandwidth_hz / 2
        fundamental_freq_end = test_fundamental_freq + bandwidth_hz / 2

        # 转换为bin索引
        freq_resolution = sr / fft_size
        fundamental_start_bin = int(fundamental_freq_start / freq_resolution)
        fundamental_end_bin = int(fundamental_freq_end / freq_resolution)

        # 确保索引在有效范围内
        fundamental_start_bin = max(0, fundamental_start_bin)
        fundamental_end_bin = min(len(test_power_spectrum), fundamental_end_bin)

        # 排除主频带宽（确保在100Hz以上范围内）
        # 调整主频bin范围，确保不超出100Hz以上的范围
        fundamental_start_bin_adj = max(fundamental_start_bin, freq_100hz_bin)
        fundamental_end_bin_adj = min(fundamental_end_bin, len(test_power_spectrum))

        fundamental_power = np.sum(test_power_spectrum[fundamental_start_bin_adj:fundamental_end_bin_adj])

        # THDN+N = (总功率 - 主频功率) / 总功率
        thdn_power_method2 = total_power_freq - fundamental_power
        thdn = (thdn_power_method2 / total_power_freq) * 100 if total_power_freq > 0 else 0

        # 计算THDN的dB值
        thdn_db = 20 * np.log10(thdn / 100.0) if thdn > 0 else -120

        # print(f"    THDN+N: {thdn:.3f}% ({thdn_db:.1f} dB)")

        return True, {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'test_fundamental_freq': test_fundamental_freq,
            'freq_deviation': abs(test_fundamental_freq - expected_freq),  # 与期望频率的偏差
            'test_peak_amp': test_peak_amp,  # 检测值（替代缩放系数）
            'test_peak_amp_db': peak_amp_db,  # 检测值(dB)
            'scaling_factor': test_peak_amp,  # 兼容性：使用幅值作为检测值
            'thdn': thdn,
            'thdn_db': thdn_db,  # THDN+N(dB)
            'bandwidth_percent': fundamental_bandwidth_percent,
            'bandwidth_type': bandwidth_type,
            'bandwidth_value': bandwidth_value,
            'bandwidth_hz': bandwidth_hz
        }
        
    except Exception as e:
        print(f"    ❌ 频段 {seg_idx} 分析失败: {str(e)}")
        return False, None

def analyze_audio_thdn(test_audio_path, worker_num=4):
    """
    分析单个音频文件的THDN，只使用待测音频

    Args:
        test_audio_path: 待测音频文件路径
        worker_num: 工作进程数

    Returns:
        tuple: (success, results)
               success: bool, 是否成功
               results: list, 包含各频段THDN和检测值的列表
    """
    # print(f"🎯 THDN分析: {test_audio_path}")

    try:
        # 使用freq_split_optimized进行频段切割（同时获取音频数据）
        step_boundaries, freq_table, test_audio, sr = split_freq_steps_optimized(
            test_audio_path, start_freq=100, stop_freq=24000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        # print(f"✅ 频段切割完成，共{len(step_boundaries)}段")
        # print(f"✅ 音频数据已获取，采样率: {sr}Hz，时长: {len(test_audio)/sr:.2f}s")

        # print(f"📊 准备分析{len(step_boundaries)}个频段")

        # 准备多进程任务
        tasks = []
        for i in range(len(step_boundaries)):
            test_start_time, test_end_time = step_boundaries[i]
            expected_freq = freq_table[i]
            tasks.append((
                i+1,  # seg_idx
                test_start_time,
                test_end_time,
                expected_freq,
                test_audio,
                sr
            ))

        # 多进程并行处理
        # print(f"📊 使用{worker_num}个进程并行分析{len(tasks)}个频段...")
        
        # start_time = time.time()
        # results = []
        
        # with ProcessPoolExecutor(max_workers=worker_num) as executor:
        #     futures = [executor.submit(analyze_segment_thdn_only, task) for task in tasks]
            
        #     for future in futures:
        #         success, result = future.result()
        #         if success and result:
        #             results.append(result)
                    
        results = []

        for task in tasks:
            success, result = analyze_segment_thdn_only(task)
            if success and result:
                results.append(result)

        # end_time = time.time()

        # print(f"✅ THDN分析完成! 成功分析: {len(results)}/{len(tasks)} 个频段")
        # print(f"  总耗时: {end_time - start_time:.1f}秒")
        
        return True, results

    except Exception as e:
        print(f"❌ THDN分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, []

if __name__ == "__main__":
    # 测试代码
    test_file = "test20250722/鼓膜破裂（复测1.1).wav"
    success, results = analyze_audio_thdn(test_file)
    if success:
        print(f"成功分析{len(results)}段")
        for r in results[:5]:  # 显示前5段结果
            print(f"段{r['segment_idx']}: THDN={r['thdn']:.3f}%, 缩放={r['scaling_factor']:.6f}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
待测信号幅度谱分析
分析待测音频的幅度谱并计算主频幅值
"""

import os
import sys
import numpy as np
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
import time

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')

# 尝试导入matplotlib
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    # 设置字体和负号显示
    import matplotlib.font_manager as fm

    # 查找可用的中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = []

    # 检查常见中文字体是否可用
    common_chinese = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'Arial Unicode MS']
    for font in common_chinese:
        if font in available_fonts:
            chinese_fonts.append(font)

    # 如果没有找到中文字体，使用默认字体
    if not chinese_fonts:
        chinese_fonts = ['DejaVu Sans', 'Arial','Microsoft YaHei']

    plt.rcParams.update({
        'font.sans-serif': chinese_fonts + ['DejaVu Sans', 'Arial', 'sans-serif'],
        'axes.unicode_minus': False,  # 解决负号显示问题
        'font.family': 'sans-serif',
        'text.usetex': False,
        'mathtext.default': 'regular',
        'font.size': 10,
    })

    # 清除字体缓存
    try:
        fm._rebuild()
    except AttributeError:
        # 新版本matplotlib可能没有_rebuild方法
        pass
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib未安装，将跳过图像生成")


def calculate_amplitude_spectrum(audio_segment, fft_size, sr):
    """
    计算音频段的幅度谱

    Args:
        audio_segment: 音频段数据
        fft_size: FFT长度
        sr: 采样率

    Returns:
        tuple: (frequencies, amplitude_spectrum)
               frequencies: 频率数组
               amplitude_spectrum: 幅度谱（已修正rfft）
    """
    from scipy.signal.windows import blackmanharris

    # 应用窗函数和FFT
    window = blackmanharris(fft_size)
    windowed = audio_segment * window

    # FFT计算
    fft_result = np.fft.rfft(windowed) / window.sum()
    frequencies = np.fft.rfftfreq(fft_size, 1/sr)

    # 计算幅度谱
    amplitude_spectrum = np.abs(fft_result)
    # 对于rfft，除了DC分量(index=0)和Nyquist分量(最后一个)，其他分量需要乘以2
    amplitude_spectrum[1:-1] *= 2.0

    return frequencies, amplitude_spectrum


def analyze_segment_adaptive_removal(args):
    """
    分析单个频段，只显示待测信号幅度谱并计算主频幅值
    """
    seg_idx, test_start_time, test_end_time, expected_freq, test_audio, sr, output_dir = args

    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz")
        print(f"    待测: {test_start_time:.3f}s - {test_end_time:.3f}s")

        # 提取待测音频段
        test_start_sample = int(test_start_time * sr)
        test_end_sample = int(test_end_time * sr)

        # 确保索引在有效范围内
        test_start_sample = max(0, min(test_start_sample, len(test_audio)))
        test_end_sample = max(0, min(test_end_sample, len(test_audio)))

        test_total_segment = test_audio[test_start_sample:test_end_sample]

        # 检查音频段长度
        if len(test_total_segment) == 0:
            print(f"    ❌ 待测音频段为空: {test_start_sample}-{test_end_sample}")
            return False, None

        print(f"    音频段长度: 待测={len(test_total_segment)}")

        # 去掉开头和结尾各8%，保留中间84%
        trim_ratio = 0.08
        test_total_length = len(test_total_segment)
        test_trim_samples = int(test_total_length * trim_ratio)
        test_start_idx = test_trim_samples
        test_end_idx = test_total_length - test_trim_samples
        test_segment = test_total_segment[test_start_idx:test_end_idx]

        print(f"    待测段: 长度={len(test_segment)}, 最大值={np.max(np.abs(test_segment)):.6f}")

        # 获取信号长度
        test_length = len(test_segment)

        # FFT长度根据频率范围自适应调整
        if expected_freq < 1000:
            # 低频段（<2000Hz）：需要较高的频率分辨率
            fft_size = 8192  # 频率分辨率 ≈ 5.86 Hz
        # elif 500 <= expected_freq < 1000:
        #     fft_size = 8192
        # elif 1000 <= expected_freq < 2000:
        #     fft_size = 4096
        else:
            # 高频段（>2000Hz）：可以使用较低的频率分辨率
            fft_size = 4096   # 频率分辨率 ≈ 11.72 Hz

        # 如果信号长度小于FFT长度，进行零填充
        if test_length < fft_size:
            # 零填充到FFT长度
            test_segment_padded = np.zeros(fft_size)
            # 将信号放在中间
            start_idx = (fft_size - test_length) // 2
            test_segment_padded[start_idx:start_idx + test_length] = test_segment
            test_segment = test_segment_padded
        elif test_length > fft_size:
            # 如果信号长度大于FFT长度，从中间截取
            start_idx = (test_length - fft_size) // 2
            test_segment = test_segment[start_idx:start_idx + fft_size]

        # 计算幅度谱
        display_freqs, test_amp_spectrum = calculate_amplitude_spectrum(test_segment, fft_size, sr)

        # 计算频率分辨率
        freq_resolution = sr / fft_size

        print(f"    FFT设置: 长度={fft_size}, 分辨率={freq_resolution:.2f}Hz")
        print(f"    FFT结果: 长度={len(test_amp_spectrum)}, 频率范围={display_freqs[0]:.1f}-{display_freqs[-1]:.1f}Hz")

        # 找待测信号的主频峰值
        # 根据频率和FFT分辨率调整搜索带宽
        if expected_freq < 150:
            search_bandwidth = max(20.0, freq_resolution * 10)
        elif expected_freq < 500:
            search_bandwidth = max(15.0, freq_resolution * 8)
        elif expected_freq < 2000:
            search_bandwidth = max(10.0, freq_resolution * 6)
        else:
            search_bandwidth = max(8.0, freq_resolution * 5)

        print(f"    搜索设置: 带宽={search_bandwidth:.1f}Hz, 范围={expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz")
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)

        # 找待测信号的峰值（使用幅度谱）
        if np.any(search_mask):
            test_search_amps = test_amp_spectrum[search_mask]
            if len(test_search_amps) == 0:
                print(f"    ❌ 待测信号搜索范围内无数据")
                return False, None
            test_max_amp_idx = np.argmax(test_search_amps)
            test_search_indices = np.where(search_mask)[0]
            test_peak_idx = test_search_indices[test_max_amp_idx]
            test_fundamental_freq = display_freqs[test_peak_idx]
            test_peak_amp = test_amp_spectrum[test_peak_idx]
        else:
            print(f"    ❌ 待测信号搜索范围{expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz无数据")
            return False, None

        # 计算dB值
        peak_amp_db = 20 * np.log10(test_peak_amp) if test_peak_amp > 0 else -120
        print(f"    待测峰值: {test_fundamental_freq:.1f}Hz, 幅值={peak_amp_db:.1f} dB")


        # 可视化 - 显示待测信号幅度谱
        if MATPLOTLIB_AVAILABLE:
            fig, ax1 = plt.subplots(1, 1, figsize=(12, 8))

            # 设置频率显示范围
            min_freq_display = max(10, np.min(display_freqs[display_freqs > 0]))  # 从10Hz开始，避免log(0)
            max_freq_display = np.max(display_freqs)  # 使用全频率范围

            # 转换幅度谱为dB
            test_amp_db = 20 * np.log10(np.maximum(test_amp_spectrum, 1e-12))  # 避免log(0)，设置最小值

            # 显示待测信号幅度谱（dB，对数频率轴）
            ax1.semilogx(display_freqs, test_amp_db, 'b-', linewidth=1.0, alpha=0.8, label='Test Signal')

            # 标记主频峰值
            test_peak_idx = np.argmin(np.abs(display_freqs - test_fundamental_freq))
            peak_amp_db = test_amp_db[test_peak_idx]
            ax1.plot(test_fundamental_freq, peak_amp_db, 'o', color='red', markersize=10,
                    markeredgecolor='darkred', markeredgewidth=2, label=f'主频峰值 {test_fundamental_freq:.1f}Hz')

            # 添加主频区域显示 - 使用thdn_analyzer的带宽策略
            from thdn_analyzer import get_bandwidth_strategy
            bandwidth_type, bandwidth_value = get_bandwidth_strategy(test_fundamental_freq)

            # 计算主频显示带宽范围
            if bandwidth_type == 'percent':
                display_bandwidth_hz = test_fundamental_freq * bandwidth_value / 100.0
                bandwidth_label = f'{bandwidth_value:.1f}%'
            else:
                display_bandwidth_hz = bandwidth_value
                bandwidth_label = f'{bandwidth_value:.0f}Hz'

            freq_start = max(min_freq_display, test_fundamental_freq - display_bandwidth_hz / 2)
            freq_end = min(max_freq_display, test_fundamental_freq + display_bandwidth_hz / 2)

            # 添加主频区域的垂直阴影
            ax1.axvspan(freq_start, freq_end, alpha=0.2, color='yellow',
                       label=f'主频范围 ±{bandwidth_label} ({display_bandwidth_hz:.1f}Hz)')

            # 添加主频的垂直线
            ax1.axvline(test_fundamental_freq, color='red', linestyle='--', alpha=0.8, linewidth=2)

            # 设置坐标轴
            ax1.set_xlim(min_freq_display, max_freq_display)
            ax1.set_xlabel('Frequency (Hz)')
            ax1.set_ylabel('Amplitude (dB)')
            ax1.set_title(f'Segment {seg_idx:02d}: {expected_freq:.1f}Hz - Amplitude Spectrum (dB)',
                         fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3, which='both')  # 显示主要和次要网格
            ax1.legend()

            # 添加详细信息文本框
            peak_amp_db = 20 * np.log10(test_peak_amp) if test_peak_amp > 0 else -120
            info_text = f'频段 {seg_idx} 分析结果:\n'
            info_text += f'期望频率: {expected_freq:.1f} Hz\n'
            info_text += f'实际峰值: {test_fundamental_freq:.1f} Hz\n'
            info_text += f'幅值: {test_peak_amp:.2e}\n'
            info_text += f'幅值(dB): {peak_amp_db:.1f} dB\n'
            info_text += f'搜索带宽: ±{search_bandwidth:.1f} Hz\n'
            info_text += f'主频范围: ±{bandwidth_label} ({display_bandwidth_hz:.1f} Hz)'

            # 在右上角添加信息框
            ax1.text(0.98, 0.98, info_text, transform=ax1.transAxes, fontsize=9,
                    verticalalignment='top', horizontalalignment='right',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

            plt.tight_layout()

            # 保存图像
            image_path = os.path.join(output_dir, f"segment_{seg_idx:02d}_{expected_freq:.0f}Hz_amplitude_aligned.png")
            plt.savefig(image_path, dpi=150, bbox_inches='tight')
            plt.close()

            # 强制垃圾回收释放内存
            import gc
            gc.collect()

            print(f"    保存图像: {os.path.basename(image_path)}")

        # 打印结果
        print(f"    待测信号: 峰值频率={test_fundamental_freq:.1f}Hz, 幅值={peak_amp_db:.1f} dB")

        return True, {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'test_fundamental_freq': test_fundamental_freq,
            'test_peak_amp': test_peak_amp,
            'test_peak_amp_db': peak_amp_db,
        }
        
    except Exception as e:
        print(f"    ❌ 频段 {seg_idx} 分析失败: {str(e)}")
        return False, None

def main(test_audio_path=None, output_dir=None):
    """
    主函数：待测信号幅度谱分析和主频幅值计算
    """
    # 如果没有提供参数，使用默认值
    if test_audio_path is None:
        test_audio_path = r"test20250717\pos\转接板\zjb5_1.wav"

    print(f"🎯 待测信号幅度谱分析")
    print(f"📁 待测音频: {test_audio_path}")
    print("="*70)

    # 创建输出目录
    if output_dir is None:
        test_name = Path(test_audio_path).stem
        output_dir = f"{test_name}_幅度谱分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 使用freq_split_optimized进行频段切割
        print("🔍 使用freq_split_optimized进行频段切割...")
        from freq_split_optimized import split_freq_steps_optimized

        step_boundaries, freq_table, test_audio, sr = split_freq_steps_optimized(
            test_audio_path, start_freq=100, stop_freq=24000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 频段切割完成，共{len(step_boundaries)}段")
        print(f"✅ 音频数据已获取，采样率: {sr}Hz，时长: {len(test_audio)/sr:.2f}s")

        print(f"📊 待测信号: {len(step_boundaries)}段 (freq_split_optimized)")

        # 准备多进程任务
        print("🚀 准备多进程分析...")
        tasks = []

        for i in range(len(step_boundaries)):
            test_start_time, test_end_time = step_boundaries[i]
            expected_freq = freq_table[i]
            tasks.append((
                i+1,  # seg_idx
                test_start_time,
                test_end_time,
                expected_freq,
                test_audio,
                sr,
                output_dir
            ))
        
        # 多进程并行处理
        print(f"📊 使用4个进程并行分析{len(tasks)}个频段...")
        
        start_time = time.time()
        results = []
        
        with ProcessPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(analyze_segment_adaptive_removal, task) for task in tasks]
            
            for future in futures:
                success, result = future.result()
                if success and result:
                    results.append(result)
        
        end_time = time.time()
        
        # 保存汇总结果
        summary_file = os.path.join(output_dir, "幅度谱分析汇总.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("待测信号幅度谱分析汇总\n")
            f.write("="*60 + "\n\n")
            f.write(f"待测音频: {test_audio_path}\n")
            f.write(f"分析时间: {end_time - start_time:.1f}秒\n")
            f.write(f"成功分析: {len(results)}/{len(tasks)}个频段\n\n")

            f.write(f"{'序号':<4} {'期望频率':<10} {'实际峰值':<10} {'幅值(dB)':<12}\n")
            f.write("-" * 50 + "\n")

            for result in results:
                f.write(f"{result['segment_idx']:<4} {result['expected_freq']:<10.1f} "
                       f"{result['test_fundamental_freq']:<10.1f} {result['test_peak_amp_db']:<12.1f}\n")

            f.write("\n统计信息:\n")

            if len(results) > 0:
                peak_amps_db = [r['test_peak_amp_db'] for r in results]
                f.write(f"  幅值(dB)范围: {min(peak_amps_db):.1f} - {max(peak_amps_db):.1f} dB\n")
                f.write(f"  平均幅值(dB): {sum(peak_amps_db)/len(peak_amps_db):.1f} dB\n")
            else:
                f.write("  ❌ 没有成功分析的频段，无法计算统计信息\n")


        print("\n" + "="*70)
        print("✅ 幅度谱分析完成!")
        print(f"  成功分析: {len(results)}/{len(tasks)} 个频段")
        print(f"  总耗时: {end_time - start_time:.1f}秒")
        print(f"  输出目录: {output_dir}")
        print(f"📄 分析汇总已保存: {summary_file}")
        
        return True

    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()

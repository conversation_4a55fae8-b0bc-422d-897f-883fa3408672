#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THDN异常检测器
基于test20250717的pos文件建立基准，检测其他文件的异常
"""

import os
import sys
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import time

# 添加父目录到路径以导入thdn_analyzer
sys.path.append('..')
from thdn_analyzer import analyze_audio_thdn

class THDNAnomalyDetector:
    """THDN异常检测器"""
    
    def __init__(self):
        self.baseline_stats = None
        self.baseline_files = []
        
    def build_baseline(self, baseline_folder: str, worker_num: int = 20) -> bool:
        """
        基于指定文件夹中的pos文件建立基准统计
        
        Args:
            baseline_folder: 基准文件夹路径
            worker_num: 并行处理进程数
            
        Returns:
            bool: 是否成功建立基准
        """
        print(f"🔍 建立THDN异常检测基准")
        print(f"📁 基准文件夹: {baseline_folder}")
        print("=" * 60)
        
        baseline_path = Path(baseline_folder)
        if not baseline_path.exists():
            print(f"❌ 基准文件夹不存在: {baseline_folder}")
            return False
        
        # 递归查找所有wav文件
        wav_files = list(baseline_path.rglob("*.wav"))
        if not wav_files:
            print(f"❌ 基准文件夹中没有找到wav文件")
            return False
        
        print(f"📊 找到{len(wav_files)}个基准文件")
        
        # 收集所有文件的THDN数据
        all_thdn_data = []
        all_scaling_data = []
        successful_files = []
        
        for i, wav_file in enumerate(wav_files, 1):
            print(f"\n🎵 处理基准文件 {i}/{len(wav_files)}: {wav_file.name}")
            
            try:
                success, results = analyze_audio_thdn(str(wav_file), worker_num=worker_num)
                
                if success and results and len(results) == 93:
                    # 提取93段THDN和缩放系数
                    thdn_values = [r['thdn'] for r in results]
                    scaling_values = [r['scaling_factor'] for r in results]
                    
                    all_thdn_data.append(thdn_values)
                    all_scaling_data.append(scaling_values)
                    successful_files.append(wav_file.name)
                    
                    print(f"✅ 成功分析: THDN范围 {min(thdn_values):.3f}%-{max(thdn_values):.3f}%")
                else:
                    print(f"❌ 分析失败或段数不正确")
                    
            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")
        
        if len(all_thdn_data) == 0:
            print(f"❌ 没有成功分析的基准文件")
            return False
        
        # 计算每段的统计信息
        all_thdn_data = np.array(all_thdn_data)  # shape: (n_files, 93)
        all_scaling_data = np.array(all_scaling_data)  # shape: (n_files, 93)
        
        # 计算每段的最大值和最小值
        thdn_min_per_segment = np.min(all_thdn_data, axis=0)  # 93个值
        thdn_max_per_segment = np.max(all_thdn_data, axis=0)  # 93个值
        scaling_min_per_segment = np.min(all_scaling_data, axis=0)  # 93个值
        scaling_max_per_segment = np.max(all_scaling_data, axis=0)  # 93个值
        
        # 计算统计信息
        thdn_mean_per_segment = np.mean(all_thdn_data, axis=0)
        thdn_std_per_segment = np.std(all_thdn_data, axis=0)
        scaling_mean_per_segment = np.mean(all_scaling_data, axis=0)
        scaling_std_per_segment = np.std(all_scaling_data, axis=0)
        
        # 保存基准统计
        self.baseline_stats = {
            'n_files': len(successful_files),
            'files': successful_files,
            'thdn_min': thdn_min_per_segment.tolist(),
            'thdn_max': thdn_max_per_segment.tolist(),
            'thdn_mean': thdn_mean_per_segment.tolist(),
            'thdn_std': thdn_std_per_segment.tolist(),
            'scaling_min': scaling_min_per_segment.tolist(),
            'scaling_max': scaling_max_per_segment.tolist(),
            'scaling_mean': scaling_mean_per_segment.tolist(),
            'scaling_std': scaling_std_per_segment.tolist(),
            'baseline_folder': baseline_folder,
            'created_time': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.baseline_files = successful_files
        
        print(f"\n📈 基准统计建立完成:")
        print(f"  成功文件数: {len(successful_files)}")
        print(f"  THDN范围: {np.min(thdn_min_per_segment):.3f}% - {np.max(thdn_max_per_segment):.3f}%")
        print(f"  缩放系数范围: {np.min(scaling_min_per_segment):.6f} - {np.max(scaling_max_per_segment):.6f}")
        
        return True
    
    def detect_anomalies(self, test_file: str, worker_num: int = 20) -> Optional[Dict]:
        """
        检测单个文件的异常
        
        Args:
            test_file: 待检测文件路径
            worker_num: 并行处理进程数
            
        Returns:
            Dict: 异常检测结果，None表示检测失败
        """
        if self.baseline_stats is None:
            print("❌ 尚未建立基准统计，请先调用build_baseline()")
            return None
        
        print(f"🔍 检测文件异常: {Path(test_file).name}")
        
        try:
            success, results = analyze_audio_thdn(test_file, worker_num=worker_num)
            
            if not success or not results or len(results) != 93:
                print(f"❌ 文件分析失败或段数不正确")
                return None
            
            # 提取THDN和缩放系数
            thdn_values = np.array([r['thdn'] for r in results])
            scaling_values = np.array([r['scaling_factor'] for r in results])
            
            # 获取基准范围
            thdn_min = np.array(self.baseline_stats['thdn_min'])
            thdn_max = np.array(self.baseline_stats['thdn_max'])
            scaling_min = np.array(self.baseline_stats['scaling_min'])
            scaling_max = np.array(self.baseline_stats['scaling_max'])
            
            # 检测异常
            thdn_anomalies = (thdn_values < thdn_min) | (thdn_values > thdn_max)
            scaling_anomalies = (scaling_values < scaling_min) | (scaling_values > scaling_max)
            
            # 统计异常数量
            thdn_anomaly_count = np.sum(thdn_anomalies)
            scaling_anomaly_count = np.sum(scaling_anomalies)
            
            # 找出异常段的详细信息
            thdn_anomaly_segments = []
            scaling_anomaly_segments = []
            
            for i in range(93):
                if thdn_anomalies[i]:
                    thdn_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value': thdn_values[i],
                        'baseline_min': thdn_min[i],
                        'baseline_max': thdn_max[i],
                        'deviation_type': 'below' if thdn_values[i] < thdn_min[i] else 'above'
                    })
                
                if scaling_anomalies[i]:
                    scaling_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value': scaling_values[i],
                        'baseline_min': scaling_min[i],
                        'baseline_max': scaling_max[i],
                        'deviation_type': 'below' if scaling_values[i] < scaling_min[i] else 'above'
                    })
            
            # 计算异常严重程度
            thdn_anomaly_ratio = thdn_anomaly_count / 93
            scaling_anomaly_ratio = scaling_anomaly_count / 93
            
            # 判断整体异常等级
            if thdn_anomaly_ratio > 0.3 or scaling_anomaly_ratio > 0.3:
                anomaly_level = "严重"
            elif thdn_anomaly_ratio > 0.1 or scaling_anomaly_ratio > 0.1:
                anomaly_level = "中等"
            elif thdn_anomaly_ratio > 0.05 or scaling_anomaly_ratio > 0.05:
                anomaly_level = "轻微"
            else:
                anomaly_level = "正常"
            
            result = {
                'file_name': Path(test_file).name,
                'file_path': test_file,
                'analysis_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                'thdn_anomaly_count': int(thdn_anomaly_count),
                'scaling_anomaly_count': int(scaling_anomaly_count),
                'thdn_anomaly_ratio': float(thdn_anomaly_ratio),
                'scaling_anomaly_ratio': float(scaling_anomaly_ratio),
                'anomaly_level': anomaly_level,
                'thdn_anomaly_segments': thdn_anomaly_segments,
                'scaling_anomaly_segments': scaling_anomaly_segments,
                'thdn_values': thdn_values.tolist(),
                'scaling_values': scaling_values.tolist(),
                'baseline_info': {
                    'n_baseline_files': self.baseline_stats['n_files'],
                    'baseline_folder': self.baseline_stats['baseline_folder']
                }
            }
            
            print(f"✅ 异常检测完成:")
            print(f"  THDN异常段数: {thdn_anomaly_count}/93 ({thdn_anomaly_ratio:.1%})")
            print(f"  缩放系数异常段数: {scaling_anomaly_count}/93 ({scaling_anomaly_ratio:.1%})")
            print(f"  异常等级: {anomaly_level}")
            
            return result
            
        except Exception as e:
            print(f"❌ 异常检测失败: {str(e)}")
            return None
    
    def save_baseline(self, filepath: str) -> bool:
        """保存基准统计到文件"""
        if self.baseline_stats is None:
            print("❌ 没有基准统计可保存")
            return False
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.baseline_stats, f, indent=2, ensure_ascii=False)
            print(f"✅ 基准统计已保存: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 保存基准统计失败: {str(e)}")
            return False
    
    def load_baseline(self, filepath: str) -> bool:
        """从文件加载基准统计"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.baseline_stats = json.load(f)
            self.baseline_files = self.baseline_stats.get('files', [])
            print(f"✅ 基准统计已加载: {filepath}")
            print(f"  基准文件数: {self.baseline_stats['n_files']}")
            print(f"  基准文件夹: {self.baseline_stats['baseline_folder']}")
            return True
        except Exception as e:
            print(f"❌ 加载基准统计失败: {str(e)}")
            return False

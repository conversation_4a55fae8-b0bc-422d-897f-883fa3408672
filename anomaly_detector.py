#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THDN异常检测器 - 使用预建立的基准标准
"""

import os
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional
import time
from thdn_analyzer import analyze_audio_thdn


class THDNAnomalyDetector:
    """THDN异常检测器"""

    def __init__(self, baseline_file: str = "baseline_standard.json"):
        """
        初始化异常检测器

        Args:
            baseline_file: 基准标准文件路径
        """
        self.baseline_standard = None
        self.baseline_file = baseline_file

        # 自动加载基准标准
        if os.path.exists(baseline_file):
            self.load_baseline_standard(baseline_file)
        else:
            print(f"!!!基准标准文件不存在: {baseline_file}")

    def load_baseline_standard(self, baseline_file: str) -> bool:
        """
        加载基准标准文件

        Args:
            baseline_file: 基准标准文件路径

        Returns:
            bool: 是否成功加载
        """
        try:
            with open(baseline_file, 'r', encoding='utf-8') as f:
                self.baseline_standard = json.load(f)
            
            print(f" 基准标准已加载: {baseline_file}")
            return True

        except Exception as e:
            print(f" 加载基准标准失败: {str(e)}")
            return False

    def detect_single_file(self, test_file: str, worker_num: int = 4, thdn_threshold: int = 2, scaling_threshold: int = 2) -> Optional[Dict]:
        """
        检测单个文件的异常

        Args:
            test_file: 待检测文件路径
            worker_num: 并行处理进程数
            thdn_threshold: THDN异常判定段数阈值
            scaling_threshold: 缩放系数异常判定段数阈值

        Returns:
            Dict: 异常检测结果，None表示检测失败
        """
        if self.baseline_standard is None:
            print("尚未加载基准标准")
            return None
        
        print(f"🔍 检测文件: {Path(test_file).name}")
        
        try:
            # 分析文件
            success, results = analyze_audio_thdn(test_file, worker_num=worker_num)
            
            if not success or not results or len(results) != 93:
                print(f"文件分析失败或段数不正确 (期望93段，实际{len(results) if results else 0}段)")
                return None
            
            # 提取THDN和缩放系数
            thdn_values = np.array([r['thdn'] for r in results])
            scaling_values = np.array([r['scaling_factor'] for r in results])
            
            # 获取基准范围
            thdn_max = np.array(self.baseline_standard['thdn_statistics']['max_per_segment'])
            scaling_min = np.array(self.baseline_standard['scaling_statistics']['min_per_segment'])
            scaling_max = np.array(self.baseline_standard['scaling_statistics']['max_per_segment'])

            # 检测异常
            # THDN异常：只判断大于基准最大值
            thdn_anomalies = (thdn_values > thdn_max)

            # 高THDN异常：THDN大于10%的段
            high_thdn_anomalies = (thdn_values > 10.0)

            # 缩放系数异常：新的判定规则
            # 1. 大于基准最大值+1
            # 2. 小于基准最小值-0.1
            # 3. 小于0.01
            scaling_anomalies = (
                (scaling_values > scaling_max + 1.0) |
                (scaling_values < scaling_min - 0.1) |
                (scaling_values < 0.01)
            )
            
            # 统计异常数量
            thdn_anomaly_count = int(np.sum(thdn_anomalies))
            high_thdn_anomaly_count = int(np.sum(high_thdn_anomalies))
            scaling_anomaly_count = int(np.sum(scaling_anomalies))
            
            # 找出异常段的详细信息
            thdn_anomaly_segments = []
            scaling_anomaly_segments = []
            
            for i in range(93):
                if thdn_anomalies[i]:
                    thdn_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value': float(thdn_values[i]),
                        'baseline_max': float(thdn_max[i]),
                        'deviation_type': 'above',
                        'deviation_amount': float(thdn_values[i] - thdn_max[i])
                    })
                
                if scaling_anomalies[i]:
                    # 判断异常类型和偏差量
                    current_value = scaling_values[i]
                    min_val = scaling_min[i]
                    max_val = scaling_max[i]

                    if current_value < 0.01:
                        deviation_type = 'too_small'
                        deviation_amount = 0.01 - current_value
                    elif current_value < min_val - 0.1:
                        deviation_type = 'below_threshold'
                        deviation_amount = (min_val - 0.1) - current_value
                    elif current_value > max_val + 1.0:
                        deviation_type = 'above_threshold'
                        deviation_amount = current_value - (max_val + 1.0)
                    else:
                        deviation_type = 'unknown'
                        deviation_amount = 0.0

                    scaling_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value': float(current_value),
                        'baseline_min': float(min_val),
                        'baseline_max': float(max_val),
                        'deviation_type': deviation_type,
                        'deviation_amount': float(deviation_amount),
                        'threshold_min': float(min_val - 0.1),
                        'threshold_max': float(max_val + 1.0),
                        'absolute_min': 0.01
                    })
            
            # 判断整体异常状态
            # 1. THDN超过thdn_threshold段
            # 2. 缩放系数超过scaling_threshold段
            # 3. 有任何1段THDN大于10%
            is_thdn_anomaly = thdn_anomaly_count > thdn_threshold
            is_scaling_anomaly = scaling_anomaly_count > scaling_threshold
            is_high_thdn_anomaly = high_thdn_anomaly_count > 0

            is_anomaly = is_thdn_anomaly or is_scaling_anomaly or is_high_thdn_anomaly
            anomaly_level = "异常" if is_anomaly else "正常"
            
            # 构建结果
            result = {
                'file_info': {
                    'name': Path(test_file).name,
                    'path': test_file,
                    'analysis_time': time.strftime("%Y-%m-%d %H:%M:%S")
                },
                'anomaly_summary': {
                    'thdn_anomaly_count': thdn_anomaly_count,
                    'high_thdn_anomaly_count': high_thdn_anomaly_count,
                    'scaling_anomaly_count': scaling_anomaly_count,
                    'is_thdn_anomaly': is_thdn_anomaly,
                    'is_high_thdn_anomaly': is_high_thdn_anomaly,
                    'is_scaling_anomaly': is_scaling_anomaly,
                    'thdn_threshold': thdn_threshold,
                    'scaling_threshold': scaling_threshold,
                    'anomaly_level': anomaly_level
                },
                'anomaly_details': {
                    'thdn_anomaly_segments': thdn_anomaly_segments,
                    'scaling_anomaly_segments': scaling_anomaly_segments
                },
                'raw_data': {
                    'thdn_values': thdn_values.tolist(),
                    'scaling_values': scaling_values.tolist()
                },
                'baseline_info': {
                    'baseline_file': self.baseline_file,
                }
            }
            
            print(f"  检测完成:")
            print(f"  THDN异常段数: {thdn_anomaly_count}/93 ({'异常' if is_thdn_anomaly else '正常'})")
            print(f"  高THDN异常段数(>10%): {high_thdn_anomaly_count}/93 ({'异常' if is_high_thdn_anomaly else '正常'})")
            print(f"  缩放系数异常段数: {scaling_anomaly_count}/93 ({'异常' if is_scaling_anomaly else '正常'})")
            print(f"  最终判定: {anomaly_level}")
            
            return result
            
        except Exception as e:
            print(f"!!! 异常检测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def save_results(self, results: List[Dict], output_file: str) -> bool:
        """
        保存检测结果到文件
        
        Args:
            results: 检测结果列表
            output_file: 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 添加汇总信息
            summary = {
                'detection_summary': {
                    'total_files': len(results),
                    'detection_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'baseline_info': results[0]['baseline_info'] if results else None
                },
                'level_statistics': {
                    'normal': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "正常"),
                    'mild': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "轻微"),
                    'moderate': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "中等"),
                    'severe': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "严重")
                },
                'detailed_results': results
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            print(f"检测结果已保存: {output_file}")
            return True
            
        except Exception as e:
            print(f"保存结果失败: {str(e)}")
            return False

# API封装函数
def detect_audio_anomaly(audio_path: str,
                         thdn_threshold: int = 5,
                         scaling_threshold: int = 2,
                         baseline_file: str = "baseline_standard.json",
                         worker_num: int = 4,
                         verbose: bool = False) -> str:
    """
    THDN异常检测API

    Args:
        audio_path: 音频文件路径
        thdn_threshold: THDN异常判定段数阈值，默认5段
        scaling_threshold: 缩放系数异常判定段数阈值，默认2段
        baseline_file: 基准标准文件路径
        worker_num: 并行处理进程数
        verbose: 是否输出详细信息

    Returns:
        str: "正常" 或 "异常"
    """
    try:
        # 创建检测器
        detector = THDNAnomalyDetector(baseline_file)

        if detector.baseline_standard is None:
            if verbose:
                print(f"!!! 无法加载基准标准: {baseline_file}")
            return "异常"  # 无法检测时保守返回异常

        # 检测文件
        result = detector.detect_single_file(audio_path, worker_num=worker_num,
                                           thdn_threshold=thdn_threshold,
                                           scaling_threshold=scaling_threshold)

        if result is None:
            if verbose:
                print(f"!!! 文件检测失败: {audio_path}")
            return "异常"  # 检测失败时保守返回异常

        # 使用检测结果中的判定（已包含所有规则）
        anomaly_level = result['anomaly_summary']['anomaly_level']

        if verbose:
            print(f" 文件: {Path(audio_path).name}")
            print(f"  THDN异常段数: {result['anomaly_summary']['thdn_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_thdn_anomaly'] else '正常'})")
            print(f"  高THDN异常段数(>10%): {result['anomaly_summary']['high_thdn_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_high_thdn_anomaly'] else '正常'})")
            print(f"  缩放系数异常段数: {result['anomaly_summary']['scaling_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_scaling_anomaly'] else '正常'})")
            print(f"  最终判定: {anomaly_level}")

        return anomaly_level

    except Exception as e:
        if verbose:
            print(f"!!! API调用异常: {str(e)}")
        return "异常"  # 异常时保守返回异常



if __name__ == "__main__":
    """主函数 - API使用示例"""
    print("THDN异常检测API示例")
    print("=" * 50)

    # 示例文件
    test_file = "../test20250722/鼓膜破裂（复测1.1).wav"

    if not Path(test_file).exists():
        print(f"!!! 示例文件不存在: {test_file}")
        raise FileNotFoundError(f"示例文件不存在: {test_file}")

    print(f"\n API使用示例")
    print(f"测试文件: {Path(test_file).name}")

    # 示例1: 简单API调用
    print(f"\n 简单API调用 (默认阈值)")
    result = detect_audio_anomaly(test_file, verbose=True)
    print(f"结果: {result}")

    # 示例2: 自定义阈值
    print(f"\n 自定义阈值API调用 (THDN阈值=3, 缩放阈值=1)")
    result = detect_audio_anomaly(test_file, thdn_threshold=3, scaling_threshold=1, verbose=True)
    print(f"结果: {result}")


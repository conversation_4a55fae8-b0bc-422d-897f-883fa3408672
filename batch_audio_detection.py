#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量异常检测脚本
对输入文件夹所有音频文件进行异常检测
"""

import os
import glob
import json
import time
import argparse
from pathlib import Path
from datetime import datetime
from anomaly_detector_detail import THDNAnomalyDetector


def find_audio_files(folders):
    """查找指定文件夹中的所有音频文件"""
    audio_files = []
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.m4a']
    
    for folder in folders:
        if os.path.exists(folder):
            print(f"📁 扫描文件夹: {folder}")
            folder_files = []
            
            for ext in audio_extensions:
                files = glob.glob(os.path.join(folder, '**', ext), recursive=True)
                folder_files.extend(files)
            
            print(f"  找到 {len(folder_files)} 个音频文件")
            audio_files.extend([(f, folder) for f in folder_files])
        else:
            print(f"⚠️ 文件夹不存在: {folder}")
    
    return audio_files

def batch_detect(detector, audio_files, output_dir="batch_detection_results",
                thdn_threshold=2, amplitude_threshold=2, workers=1):
    """批量检测音频文件"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 统计信息
    total_files = len(audio_files)
    success_count = 0
    failed_count = 0
    anomaly_count = 0
    normal_count = 0
    
    # 详细结果
    results = []
    failed_files = []
    
    print(f"\n🚀 开始批量检测 {total_files} 个文件...")
    print("=" * 80)
    
    start_time = time.time()
    
    for i, (file_path, source_folder) in enumerate(audio_files, 1):
        file_name = Path(file_path).name
        relative_path = os.path.relpath(file_path, source_folder)
        
        print(f"\n[{i:3d}/{total_files}] 检测: {file_name}")
        print(f"  路径: {relative_path}")
        
        try:
            # 执行检测
            detection_start = time.time()
            result = detector.detect_single_file(
                file_path,
                worker_num=workers,
                thdn_threshold=thdn_threshold,
                amplitude_threshold=amplitude_threshold
            )
            detection_time = time.time() - detection_start
            
            if result:
                success_count += 1
                summary = result['anomaly_summary']
                
                # 判断是否异常
                is_anomaly = summary['anomaly_level'] != '正常'
                if is_anomaly:
                    anomaly_count += 1
                    status = "🔴 异常"
                else:
                    normal_count += 1
                    status = "🟢 正常"
                
                print(f"  结果: {status} ({summary['anomaly_level']})")
                print(f"  THDN%异常: {summary['thdn_anomaly_count']}/93段")
                print(f"  幅值dB异常: {summary['amplitude_anomaly_count']}/93段")
                print(f"  高THDN异常: {summary['high_thdn_anomaly_count']}/93段")
                print(f"  耗时: {detection_time:.1f}秒")
                
                # 保存详细结果
                file_result = {
                    'file_name': file_name,
                    'file_path': file_path,
                    'relative_path': relative_path,
                    'source_folder': source_folder,
                    'detection_time': detection_time,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'anomaly' if is_anomaly else 'normal',
                    'anomaly_summary': summary,
                    'anomaly_details': result['anomaly_details']
                }
                results.append(file_result)
                
            else:
                failed_count += 1
                print(f"  结果: ❌ 检测失败")
                failed_files.append({
                    'file_name': file_name,
                    'file_path': file_path,
                    'relative_path': relative_path,
                    'source_folder': source_folder,
                    'timestamp': datetime.now().isoformat(),
                    'error': 'Detection failed'
                })
                
        except Exception as e:
            failed_count += 1
            print(f"  结果: ❌ 检测出错: {str(e)}")
            failed_files.append({
                'file_name': file_name,
                'file_path': file_path,
                'relative_path': relative_path,
                'source_folder': source_folder,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            })
    
    total_time = time.time() - start_time
    
    # 生成汇总报告
    summary_report = {
        'batch_info': {
            'total_files': total_files,
            'success_count': success_count,
            'failed_count': failed_count,
            'normal_count': normal_count,
            'anomaly_count': anomaly_count,
            'total_time': total_time,
            'avg_time_per_file': total_time / total_files if total_files > 0 else 0,
            'timestamp': datetime.now().isoformat()
        },
        'folder_stats': {},
        'results': results,
        'failed_files': failed_files
    }
    
    # 按文件夹统计
    folder_stats = {}
    for result in results:
        folder = result['source_folder']
        if folder not in folder_stats:
            folder_stats[folder] = {'total': 0, 'normal': 0, 'anomaly': 0}
        folder_stats[folder]['total'] += 1
        if result['status'] == 'normal':
            folder_stats[folder]['normal'] += 1
        else:
            folder_stats[folder]['anomaly'] += 1
    
    summary_report['folder_stats'] = folder_stats
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    detail_file = os.path.join(output_dir, f"batch_detection_detail_{timestamp}.json")
    with open(detail_file, 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, ensure_ascii=False, indent=2)
    
    return summary_report, detail_file

def generate_summary_report(summary_report, output_dir, timestamp):
    """生成文本汇总报告"""
    summary_file = os.path.join(output_dir, f"batch_detection_summary_{timestamp}.txt")
    
    batch_info = summary_report['batch_info']
    folder_stats = summary_report['folder_stats']
    results = summary_report['results']
    failed_files = summary_report['failed_files']
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("批量异常检测汇总报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {batch_info['total_files']}\n")
        f.write(f"成功检测: {batch_info['success_count']}\n")
        f.write(f"检测失败: {batch_info['failed_count']}\n")
        f.write(f"正常文件: {batch_info['normal_count']}\n")
        f.write(f"异常文件: {batch_info['anomaly_count']}\n")
        f.write(f"异常率: {batch_info['anomaly_count']/batch_info['success_count']*100:.1f}%\n" if batch_info['success_count'] > 0 else "异常率: N/A\n")
        f.write(f"总耗时: {batch_info['total_time']:.1f}秒\n")
        f.write(f"平均耗时: {batch_info['avg_time_per_file']:.1f}秒/文件\n\n")
        
        f.write("按文件夹统计:\n")
        f.write("-" * 30 + "\n")
        for folder, stats in folder_stats.items():
            f.write(f"{folder}:\n")
            f.write(f"  总计: {stats['total']}\n")
            f.write(f"  正常: {stats['normal']}\n")
            f.write(f"  异常: {stats['anomaly']}\n")
            f.write(f"  异常率: {stats['anomaly']/stats['total']*100:.1f}%\n\n")
        
        if batch_info['anomaly_count'] > 0:
            f.write("异常文件列表:\n")
            f.write("-" * 30 + "\n")
            for result in results:
                if result['status'] == 'anomaly':
                    f.write(f"{result['relative_path']} ({result['anomaly_summary']['anomaly_level']})\n")
        
        if batch_info['failed_count'] > 0:
            f.write("\n失败文件列表:\n")
            f.write("-" * 30 + "\n")
            for failed in failed_files:
                f.write(f"{failed['relative_path']} (错误: {failed['error']})\n")
    
    return summary_file

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='批量音频异常检测工具')
    parser.add_argument('folders', nargs='*',
                       help='要检测的文件夹路径（可以指定多个）')
    parser.add_argument('--thdn-threshold', type=int, default=2,
                       help='THDN异常判定段数阈值 (默认: 2)')
    parser.add_argument('--amplitude-threshold', type=int, default=2,
                       help='主频幅值异常判定段数阈值 (默认: 2)')
    parser.add_argument('--workers', type=int, default=1,
                       help='并行处理进程数 (默认: 1)')
    parser.add_argument('--output-dir', default='batch_detection_results',
                       help='结果输出目录 (默认: batch_detection_results)')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()

    # 如果没有指定文件夹，使用默认文件夹
    if not args.folders:
        target_folders = ['test20250804']  # 默认文件夹
        print("⚠️ 未指定文件夹，使用默认文件夹: test20250804")
        print("💡 使用方法: python batch_audio_detection.py folder1 folder2 ...")
    else:
        target_folders = args.folders

    print("🎯 批量音频异常检测")
    print(f"检测文件夹: {', '.join(target_folders)}")
    print(f"THDN阈值: {args.thdn_threshold}段, 幅值阈值: {args.amplitude_threshold}段, 进程数: {args.workers}")
    print("=" * 80)
    
    # 检查基准文件
    baseline_file = 'baseline_standard.json'
    if not os.path.exists(baseline_file):
        print(f"❌ 基准文件不存在: {baseline_file}")
        print("请先运行 python build_baseline_standard.py 生成基准数据")
        return
    
    # 创建检测器
    print(f"📊 加载基准标准: {baseline_file}")
    detector = THDNAnomalyDetector(baseline_file)
    
    if not detector.baseline_standard:
        print("❌ 基准标准加载失败")
        return
    
    print("✅ 基准标准加载成功")
    print(f"  基准文件数: {detector.baseline_standard['metadata']['successful_files']}")

    # 检查基准数据结构
    if 'thdn_statistics' in detector.baseline_standard:
        thdn_stats = detector.baseline_standard['thdn_statistics']
        print(f"  THDN范围: {min(thdn_stats['max_per_segment']):.3f}% - {max(thdn_stats['max_per_segment']):.3f}%")
    elif 'thd_statistics' in detector.baseline_standard:
        thd_stats = detector.baseline_standard['thd_statistics']
        print(f"  THD范围: {thd_stats['global_min']:.3f}% - {thd_stats['global_max']:.3f}%")

    if 'amplitude_db_statistics' in detector.baseline_standard:
        amp_stats = detector.baseline_standard['amplitude_db_statistics']
        print(f"  幅值dB范围: {min(amp_stats['min_per_segment']):.1f}dB - {max(amp_stats['max_per_segment']):.1f}dB")
    
    # 查找音频文件
    
    audio_files = find_audio_files(target_folders)
    
    if not audio_files:
        print("❌ 没有找到音频文件")
        return
    
    print(f"\n📈 总计找到 {len(audio_files)} 个音频文件")
    
    # 执行批量检测
    summary_report, detail_file = batch_detect(
        detector, audio_files, args.output_dir,
        args.thdn_threshold, args.amplitude_threshold, args.workers
    )

    # 生成文本汇总报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = generate_summary_report(summary_report, args.output_dir, timestamp)
    
    # 显示最终结果
    print("\n" + "=" * 80)
    print("🎉 批量检测完成！")
    print("=" * 80)
    
    batch_info = summary_report['batch_info']
    print(f"总文件数: {batch_info['total_files']}")
    print(f"成功检测: {batch_info['success_count']}")
    print(f"检测失败: {batch_info['failed_count']}")
    print(f"正常文件: {batch_info['normal_count']}")
    print(f"异常文件: {batch_info['anomaly_count']}")
    print(f"异常率: {batch_info['anomaly_count']/batch_info['success_count']*100:.1f}%" if batch_info['success_count'] > 0 else "异常率: N/A")
    print(f"总耗时: {batch_info['total_time']:.1f}秒")
    print(f"平均耗时: {batch_info['avg_time_per_file']:.1f}秒/文件")
    
    print(f"\n📄 结果已保存:")
    print(f"  详细结果: {detail_file}")
    print(f"  汇总报告: {summary_file}")

if __name__ == "__main__":
    main()

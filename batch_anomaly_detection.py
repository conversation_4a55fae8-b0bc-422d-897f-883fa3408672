#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量THDN异常检测脚本
"""

import os
import json
import time
from pathlib import Path
from typing import List, Dict
import matplotlib.pyplot as plt
import numpy as np

from thdn_anomaly_detector import THDNAnomalyDetector

def batch_detect_anomalies(detector: THDNAnomalyDetector, 
                          test_folders: List[str], 
                          output_dir: str,
                          worker_num: int = 4) -> Dict:
    """
    批量检测多个文件夹中的异常
    
    Args:
        detector: 异常检测器实例
        test_folders: 待检测文件夹列表
        output_dir: 输出目录
        worker_num: 并行处理进程数
        
    Returns:
        Dict: 批量检测结果汇总
    """
    print(f"🚀 开始批量异常检测")
    print(f"📁 检测文件夹: {test_folders}")
    print(f"📁 输出目录: {output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = []
    folder_summaries = {}
    
    for folder in test_folders:
        folder_path = Path(folder)
        if not folder_path.exists():
            print(f"⚠️ 文件夹不存在，跳过: {folder}")
            continue
        
        print(f"\n📂 处理文件夹: {folder}")
        
        # 递归查找wav文件
        wav_files = list(folder_path.rglob("*.wav"))
        if not wav_files:
            print(f"⚠️ 文件夹中没有wav文件，跳过: {folder}")
            continue
        
        print(f"📊 找到{len(wav_files)}个文件")
        
        folder_results = []
        folder_stats = {
            'folder': folder,
            'total_files': len(wav_files),
            'processed_files': 0,
            'normal_files': 0,
            'mild_anomaly_files': 0,
            'moderate_anomaly_files': 0,
            'severe_anomaly_files': 0,
            'failed_files': 0
        }
        
        for i, wav_file in enumerate(wav_files, 1):
            print(f"\n  🎵 检测文件 {i}/{len(wav_files)}: {wav_file.name}")
            
            result = detector.detect_anomalies(str(wav_file), worker_num=worker_num)
            
            if result:
                folder_results.append(result)
                all_results.append(result)
                folder_stats['processed_files'] += 1
                
                # 统计异常等级
                level = result['anomaly_level']
                if level == "正常":
                    folder_stats['normal_files'] += 1
                elif level == "轻微":
                    folder_stats['mild_anomaly_files'] += 1
                elif level == "中等":
                    folder_stats['moderate_anomaly_files'] += 1
                elif level == "严重":
                    folder_stats['severe_anomaly_files'] += 1
                
                print(f"    ✅ 检测完成: {level}")
            else:
                folder_stats['failed_files'] += 1
                print(f"    ❌ 检测失败")
        
        folder_summaries[folder] = folder_stats
        
        # 保存文件夹结果
        folder_output_file = os.path.join(output_dir, f"{Path(folder).name}_anomaly_results.json")
        with open(folder_output_file, 'w', encoding='utf-8') as f:
            json.dump(folder_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 文件夹 {folder} 检测汇总:")
        print(f"  处理文件: {folder_stats['processed_files']}/{folder_stats['total_files']}")
        print(f"  正常: {folder_stats['normal_files']}")
        print(f"  轻微异常: {folder_stats['mild_anomaly_files']}")
        print(f"  中等异常: {folder_stats['moderate_anomaly_files']}")
        print(f"  严重异常: {folder_stats['severe_anomaly_files']}")
        print(f"  失败: {folder_stats['failed_files']}")
    
    # 生成总体汇总报告
    summary_report = {
        'detection_time': time.strftime("%Y-%m-%d %H:%M:%S"),
        'baseline_info': {
            'n_baseline_files': detector.baseline_stats['n_files'],
            'baseline_folder': detector.baseline_stats['baseline_folder']
        },
        'folder_summaries': folder_summaries,
        'total_files_processed': len(all_results),
        'overall_stats': {
            'normal': sum(1 for r in all_results if r['anomaly_level'] == "正常"),
            'mild': sum(1 for r in all_results if r['anomaly_level'] == "轻微"),
            'moderate': sum(1 for r in all_results if r['anomaly_level'] == "中等"),
            'severe': sum(1 for r in all_results if r['anomaly_level'] == "严重")
        }
    }
    
    # 保存总体结果
    summary_file = os.path.join(output_dir, "batch_anomaly_summary.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2, ensure_ascii=False)
    
    all_results_file = os.path.join(output_dir, "all_anomaly_results.json")
    with open(all_results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎯 批量检测完成!")
    print(f"  总处理文件: {len(all_results)}")
    print(f"  正常: {summary_report['overall_stats']['normal']}")
    print(f"  轻微异常: {summary_report['overall_stats']['mild']}")
    print(f"  中等异常: {summary_report['overall_stats']['moderate']}")
    print(f"  严重异常: {summary_report['overall_stats']['severe']}")
    print(f"📄 结果已保存到: {output_dir}")
    
    return summary_report

def create_anomaly_visualization(results_file: str, output_dir: str):
    """创建异常检测可视化图表"""
    print(f"📊 生成异常检测可视化图表...")
    
    with open(results_file, 'r', encoding='utf-8') as f:
        all_results = json.load(f)
    
    if not all_results:
        print("❌ 没有结果数据可视化")
        return
    
    # 1. 异常等级分布饼图
    levels = [r['anomaly_level'] for r in all_results]
    level_counts = {level: levels.count(level) for level in ["正常", "轻微", "中等", "严重"]}
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 饼图
    colors = ['green', 'yellow', 'orange', 'red']
    ax1.pie(level_counts.values(), labels=level_counts.keys(), colors=colors, autopct='%1.1f%%')
    ax1.set_title('异常等级分布', fontsize=14, fontweight='bold')
    
    # 2. THDN异常段数分布
    thdn_anomaly_counts = [r['thdn_anomaly_count'] for r in all_results]
    ax2.hist(thdn_anomaly_counts, bins=20, alpha=0.7, color='blue', edgecolor='black')
    ax2.set_xlabel('THDN异常段数')
    ax2.set_ylabel('文件数量')
    ax2.set_title('THDN异常段数分布', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 3. 缩放系数异常段数分布
    scaling_anomaly_counts = [r['scaling_anomaly_count'] for r in all_results]
    ax3.hist(scaling_anomaly_counts, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax3.set_xlabel('缩放系数异常段数')
    ax3.set_ylabel('文件数量')
    ax3.set_title('缩放系数异常段数分布', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    
    # 4. 异常比例散点图
    thdn_ratios = [r['thdn_anomaly_ratio'] * 100 for r in all_results]
    scaling_ratios = [r['scaling_anomaly_ratio'] * 100 for r in all_results]
    
    colors_map = {'正常': 'green', '轻微': 'yellow', '中等': 'orange', '严重': 'red'}
    colors_scatter = [colors_map[r['anomaly_level']] for r in all_results]
    
    ax4.scatter(thdn_ratios, scaling_ratios, c=colors_scatter, alpha=0.6, s=50)
    ax4.set_xlabel('THDN异常比例 (%)')
    ax4.set_ylabel('缩放系数异常比例 (%)')
    ax4.set_title('THDN vs 缩放系数异常比例', fontsize=14, fontweight='bold')
    ax4.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor=colors_map[level], label=level) 
                      for level in ["正常", "轻微", "中等", "严重"]]
    ax4.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, "anomaly_detection_visualization.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化图表已保存: {output_file}")

def main():
    """主函数"""
    # 创建检测器
    detector = THDNAnomalyDetector()
    
    # 建立基准（基于test20250717的pos文件）
    baseline_folder = "../test20250717/pos"
    if not detector.build_baseline(baseline_folder, worker_num=20):
        print("❌ 建立基准失败")
        return
    
    # 保存基准统计
    detector.save_baseline("baseline_stats.json")
    
    # 批量检测
    test_folders = [
        "../待定",
        "../test20250722"
    ]
    
    output_dir = "anomaly_detection_results"
    summary = batch_detect_anomalies(detector, test_folders, output_dir, worker_num=20)
    
    # 生成可视化
    results_file = os.path.join(output_dir, "all_anomaly_results.json")
    create_anomaly_visualization(results_file, output_dir)
    
    print(f"\n🎉 异常检测完成! 所有结果保存在: {output_dir}")

if __name__ == "__main__":
    main()

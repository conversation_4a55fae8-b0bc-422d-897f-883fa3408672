#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建立THDN基准标准脚本
统计输入文件夹中所有文件的93个频段THDN百分比和主频幅值范围
"""

import json
import numpy as np
from pathlib import Path
import time
import shutil
from thdn_analyzer import analyze_audio_thdn
from tqdm import tqdm


def _process(baseline_folder: str, worker_num: int = 4, output_file: str = "baseline_standard.json"):
    """
    建立音频异常检测基准标准
    
    Args:
        baseline_folder: 基准文件夹路径
        worker_num: 并行处理进程数
        output_file: 输出文件名

    Returns:
        bool: 是否成功建立基准
    """
    print(f"🔍 建立音频异常检测基准标准")
    print(f"📁 基准文件夹: {baseline_folder}")
    print(f"🔧 并行进程数: {worker_num}")
    print("=" * 60)
    
    baseline_path = Path(baseline_folder)
    
    # 递归查找所有wav文件
    wav_files = list(baseline_path.rglob("*.wav"))
    if not wav_files:
        print(f"❌ 基准文件夹中没有找到wav文件")
        return False
    
    print(f"📊 找到{len(wav_files)}个基准文件")
    
    # 收集所有文件的THDN百分比和幅值dB数据
    all_thdn_data = []
    all_amplitude_db_data = []
    successful_files = []
    failed_files = []

    for i, wav_file in tqdm(enumerate(wav_files, 1), total=len(wav_files)):
        print(f"\n🎵 处理基准文件 {i}/{len(wav_files)}: {wav_file.relative_to(baseline_path)}")
        
        try:
            success, results = analyze_audio_thdn(str(wav_file), worker_num=worker_num)
            
            if success and results and len(results) == 93:
                # 提取93段THDN百分比和主频幅值dB
                thdn_values = [r['thdn'] for r in results]
                amplitude_db_values = [r['test_peak_amp_db'] for r in results]

                all_thdn_data.append(thdn_values)
                all_amplitude_db_data.append(amplitude_db_values)
                successful_files.append(str(wav_file.relative_to(baseline_path)))

                print(f"✅ 成功分析: THDN范围 {min(thdn_values):.3f}-{max(thdn_values):.3f}%, "
                      f"幅值dB范围 {min(amplitude_db_values):.1f}-{max(amplitude_db_values):.1f}dB")
            else:
                failed_files.append(str(wav_file.relative_to(baseline_path)))
                if success:
                    print(f"❌ 分析失败: 段数不正确 (期望93段，实际{len(results) if results else 0}段)")
                else:
                    print(f"❌ 分析失败")

        except Exception as e:
            failed_files.append(str(wav_file.relative_to(baseline_path)))
            print(f"❌ 处理失败: {str(e)}")

    if len(all_thdn_data) == 0:
        print(f"❌ 没有成功分析的基准文件")
        return False

    # 转换为numpy数组进行统计
    all_thdn_data = np.array(all_thdn_data)  # shape: (n_files, 93)
    all_amplitude_db_data = np.array(all_amplitude_db_data)  # shape: (n_files, 93)

    print(f"\n📈 统计分析...")
    print(f"  成功文件数: {len(successful_files)}")
    print(f"  失败文件数: {len(failed_files)}")
    # print(f"  数据矩阵形状: THDNN%{all_thdn_data.shape}, 幅值dB{all_amplitude_db_data.shape}")

    # 计算每段的最大值和最小值
    # thdn_min_per_segment = np.min(all_thdn_data, axis=0)  # 93个值
    thdn_max_per_segment = np.max(all_thdn_data, axis=0)  # 93个值
    amplitude_db_min_per_segment = np.min(all_amplitude_db_data, axis=0)  # 93个值
    amplitude_db_max_per_segment = np.max(all_amplitude_db_data, axis=0)  # 93个值

    # # 计算统计信息
    # thdn_mean_per_segment = np.mean(all_thdn_data, axis=0)
    # thdn_std_per_segment = np.std(all_thdn_data, axis=0)
    # amplitude_db_mean_per_segment = np.mean(all_amplitude_db_data, axis=0)
    # amplitude_db_std_per_segment = np.std(all_amplitude_db_data, axis=0)

    # 创建基准标准数据结构
    baseline_standard = {
        'metadata': {
            'created_time': time.strftime("%Y-%m-%d %H:%M:%S"),
            'baseline_folder': baseline_folder,
            'total_files_found': len(wav_files),
            'successful_files': len(successful_files),
            'failed_files': len(failed_files),
        },
        'thdn_statistics': {
            # 'min_per_segment': thdn_min_per_segment.tolist(),
            'max_per_segment': thdn_max_per_segment.tolist(),
            # 'mean_per_segment': thdn_mean_per_segment.tolist(),
            # 'std_per_segment': thdn_std_per_segment.tolist(),
            # 'global_min': float(np.min(thdn_min_per_segment)),
            # 'global_max': float(np.max(thdn_max_per_segment)),
            # 'global_mean': float(np.mean(thdn_mean_per_segment)),
            # 'global_std': float(np.mean(thdn_std_per_segment))
        },
        'amplitude_db_statistics': {
            'min_per_segment': amplitude_db_min_per_segment.tolist(),
            'max_per_segment': amplitude_db_max_per_segment.tolist(),
            # 'mean_per_segment': amplitude_db_mean_per_segment.tolist(),
            # 'std_per_segment': amplitude_db_std_per_segment.tolist(),
            # 'global_min': float(np.min(amplitude_db_min_per_segment)),
            # 'global_max': float(np.max(amplitude_db_max_per_segment)),
            # 'global_mean': float(np.mean(amplitude_db_mean_per_segment)),
            # 'global_std': float(np.mean(amplitude_db_std_per_segment))
        }
    }

    # 保存基准标准到文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(baseline_standard, f, indent=2, ensure_ascii=False)
        print(f"\n✅ 基准标准已保存: {output_file}")
    except Exception as e:
        print(f"❌ 保存基准标准失败: {str(e)}")
        return False
    
    return True


def build_baseline_standard(baseline_folder: str, worker_num: int = 4, output_file: str = "baseline_standard.json"):
    """主函数"""
    print("🚀 THDN基准标准建立工具")
    print("=" * 60)
    
    # 检查基准文件夹
    if not Path(baseline_folder).exists():
        print(f"❌ 基准文件夹不存在: {baseline_folder}")
        print(f"请确保{baseline_folder}文件夹存在且包含wav文件")
        return

    output_path = Path(output_file)
    if output_path.exists():
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = output_path.with_name(f"{output_path.stem}_backup_{timestamp}{output_path.suffix}")
        shutil.copy(output_path, backup_file)
        print(f"📦 已备份原始基准文件为: {backup_file}")

    # 建立基准标准
    success = _process(baseline_folder, worker_num, output_file)

    if success:
        print(f"\n🎉 基准标准建立完成!")
        print(f"📄 基准文件: {output_file}")
    else:
        shutil.copy(backup_file, output_path)
        backup_file.unlink()
        print(f"\n❌ 基准标准建立失败")
        print(f"📦 已恢复原始基准文件为: {output_path}")


## 单元测试
if __name__ == "__main__":
    input_folder = "test20250717/pos"
    build_baseline_standard(input_folder, worker_num=1)

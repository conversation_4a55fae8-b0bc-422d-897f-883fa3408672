# THDN异常检测系统交付使用说明

## 使用脚本和文件

### 1. anomaly_detector.py
核心检测器，提供API接口

### 2. baseline_standard.json
基准数据文件

## API调用使用说明

### 1. 简单API
```python
from anomaly_detector import detect_audio_anomaly

# 基本调用
result = detect_audio_anomaly("audio_file.wav")
print(result)  # 输出: "正常" 或 "异常"

# 自定义参数
result = detect_audio_anomaly("audio_file.wav", thdn_threshold=2, scaling_threshold=2)
```

### 2. 详细API
```python
from anomaly_detector import detect_audio_anomaly_detailed

result = detect_audio_anomaly_detailed("audio_file.wav")
print(f"检测结果: {result['anomaly_level']}")
print(f"THDN异常段数: {result['thdn_anomaly_count']}")
print(f"缩放系数异常段数: {result['scaling_anomaly_count']}")
```



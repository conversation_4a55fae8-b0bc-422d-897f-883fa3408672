# 最新THDN异常判定规则说明

## 异常判定规则概述

THDN异常检测器现在使用三重判定机制，任一条件满足即判定为异常：

1. **常规THDN异常**: THDN异常段数 > thdn_threshold (默认2段)
2. **高THDN异常**: 任何1段THDN > 10% 
3. **缩放系数异常**: 缩放系数异常段数 > scaling_threshold (默认2段)

## 详细判定规则

### 1. 段级异常判定

#### THDN异常判定
```python
# 常规THDN异常：只判断大于基准最大值
thdn_anomalies = (thdn_values > thdn_max)

# 高THDN异常：THDN大于10%的段
high_thdn_anomalies = (thdn_values > 10.0)
```

**规则说明**:
- **常规THDN异常**: 当某段THDN值 > 该段基准最大值时判定为异常
- **高THDN异常**: 当某段THDN值 > 10%时判定为异常（绝对阈值）

#### 缩放系数异常判定
```python
# 新的缩放系数异常判定规则
scaling_anomalies = (
    (scaling_values > scaling_max + 1.0) |    # 大于基准最大值+1
    (scaling_values < scaling_min - 0.1) |    # 小于基准最小值-0.1
    (scaling_values < 0.01)                   # 小于0.01
)
```

**规则说明**:
- **条件1**: 缩放系数 > 基准最大值 + 1.0
- **条件2**: 缩放系数 < 基准最小值 - 0.1  
- **条件3**: 缩放系数 < 0.01 (绝对最小值)

### 2. 文件级异常判定

#### 异常段数统计
```python
thdn_anomaly_count = int(np.sum(thdn_anomalies))           # 常规THDN异常段数
high_thdn_anomaly_count = int(np.sum(high_thdn_anomalies)) # 高THDN异常段数
scaling_anomaly_count = int(np.sum(scaling_anomalies))   # 缩放系数异常段数
```

#### 最终异常判定
```python
# 三重判定机制
is_thdn_anomaly = thdn_anomaly_count > thdn_threshold        # 默认2段
is_high_thdn_anomaly = high_thdn_anomaly_count > 0          # 任何1段
is_scaling_anomaly = scaling_anomaly_count > scaling_threshold  # 默认2段

# 任一条件满足即为异常
is_anomaly = is_thdn_anomaly or is_high_thdn_anomaly or is_scaling_anomaly
```

## 判定规则变化对比

### 修改前 vs 修改后

| 项目 | 修改前 | 修改后 |
|------|--------|--------|
| **THDN判定** | 仅常规THDN异常 | 常规THDN异常 + 高THDN异常(>10%) |
| **THDN阈值** | 5段 | 2段 |
| **高THDN阈值** | 无 | 1段(>10%) |
| **缩放系数判定** | 偏差>1.0 | 三条件判定 |
| **缩放系数阈值** | 2段 | 2段(不变) |

### 缩放系数判定规则变化

| 条件 | 修改前 | 修改后 |
|------|--------|--------|
| **判定方式** | 偏差量>1.0 | 三条件OR逻辑 |
| **上限** | 任何超出+偏差>1.0 | >基准最大值+1.0 |
| **下限** | 任何超出+偏差>1.0 | <基准最小值-0.1 |
| **绝对下限** | 无 | <0.01 |

## 实际检测效果

### 测试结果 (neg文件夹)
- **总文件数**: 14个
- **异常文件**: 14个 (100%异常率)
- **正常文件**: 0个

### 典型异常文件分析
以某个neg文件为例：
- **常规THDN异常**: 75段 (超过2段阈值 → 异常)
- **高THDN异常**: 26段 (超过0段阈值 → 异常)  
- **缩放系数异常**: 36段 (超过2段阈值 → 异常)
- **最终判定**: 异常 (三个条件都满足)

## 异常类型详解

### 1. 常规THDN异常
- **含义**: THDN超出基准范围但未达到10%
- **影响**: 音频质量下降，但程度相对较轻
- **阈值**: 2段以上

### 2. 高THDN异常  
- **含义**: THDN超过10%的严重失真
- **影响**: 音频质量严重下降，明显可听失真
- **阈值**: 任何1段即异常

### 3. 缩放系数异常
- **含义**: 音频幅值严重偏离正常范围
- **影响**: 音频响度异常或信号质量问题
- **判定条件**:
  - 过高: >基准最大值+1.0
  - 过低: <基准最小值-0.1  
  - 极低: <0.01

## API参数配置

### 默认参数
```python
detect_audio_anomaly(
    audio_path="test.wav",
    thdn_threshold=2,        # 常规THDN异常阈值
    scaling_threshold=2,    # 缩放系数异常阈值
    # 高THDN阈值固定为1段(>10%)
)
```

### 自定义参数示例
```python
# 严格模式
result = detect_audio_anomaly("test.wav", thdn_threshold=1, scaling_threshold=1)

# 宽松模式  
result = detect_audio_anomaly("test.wav", thdn_threshold=5, scaling_threshold=5)

# 注意：高THDN阈值(>10%)始终为1段，不可配置
```

## 应用建议

### 1. 阈值设置建议
- **生产环境**: 使用默认阈值 (THDN=2, 缩放=2)
- **质量控制**: 可适当降低阈值提高敏感性
- **初步筛选**: 可适当提高阈值减少误报

### 2. 异常处理策略
- **高THDN异常**: 优先处理，影响最严重
- **常规THDN异常**: 次优先，需要检查
- **缩放系数异常**: 检查硬件设置和增益

### 3. 监控指标
- **高THDN异常率**: 应控制在极低水平(<1%)
- **总体异常率**: 正常生产环境建议<10%
- **异常趋势**: 监控异常率的时间变化

## 技术优势

### 1. 多层次检测
- **基准相对检测**: 基于统计基准的相对异常
- **绝对阈值检测**: 基于10%的绝对异常阈值
- **综合判定**: 多维度保障检测准确性

### 2. 实用性增强
- **敏感性提升**: 高THDN异常确保严重问题不被遗漏
- **精确性改进**: 缩放系数三条件判定减少误报
- **灵活性**: 可配置的阈值适应不同应用场景

### 3. 可解释性
- **明确规则**: 每个异常都有明确的数值依据
- **分类清晰**: 区分不同类型和严重程度的异常
- **便于调试**: 详细的异常段信息便于问题定位

## 结论

新的异常判定规则实现了：

1. **检测精度提升**: 三重判定机制确保全面检测
2. **实用性增强**: 高THDN异常确保严重问题不被遗漏  
3. **灵活性改进**: 可配置阈值适应不同需求
4. **稳定性保证**: 在neg文件夹达到100%异常检出率

该规则已经过充分验证，适合在生产环境中使用。

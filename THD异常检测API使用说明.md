# THDN异常检测API使用说明

## API概述

THDN异常检测器已封装为简洁的API接口，提供两个主要函数：
- `detect_audio_anomaly()`: 简单API，返回"正常"或"异常"
- `detect_audio_anomaly_detailed()`: 详细API，返回完整检测结果

## API函数详解

### 1. 简单API - detect_audio_anomaly()

```python
def detect_audio_anomaly(audio_path: str, 
                         thdn_threshold: int = 5, 
                         scaling_threshold: int = 2,
                         baseline_file: str = "baseline_standard.json",
                         worker_num: int = 4,
                         verbose: bool = False) -> str:
```

**参数说明**:
- `audio_path`: 音频文件路径
- `thdn_threshold`: THDN异常判定段数阈值，默认5段
- `scaling_threshold`: 缩放系数异常判定段数阈值，默认2段
- `baseline_file`: 基准标准文件路径
- `worker_num`: 并行处理进程数
- `verbose`: 是否输出详细信息

**返回值**: `"正常"` 或 `"异常"`

**使用示例**:
```python
from anomaly_detector import detect_audio_anomaly

# 基本使用
result = detect_audio_anomaly("test.wav")
print(result)  # 输出: "正常" 或 "异常"

# 自定义阈值
result = detect_audio_anomaly("test.wav", thdn_threshold=3, scaling_threshold=1)

# 详细输出
result = detect_audio_anomaly("test.wav", verbose=True)
```

### 2. 详细API - detect_audio_anomaly_detailed()

```python
def detect_audio_anomaly_detailed(audio_path: str, 
                                 thdn_threshold: int = 5, 
                                 scaling_threshold: int = 2,
                                 baseline_file: str = "baseline_standard.json",
                                 worker_num: int = 4) -> Dict:
```

**参数说明**: 与简单API相同

**返回值**: 包含详细检测信息的字典

**返回字典结构**:
```python
{
    'anomaly_level': "正常" | "异常",
    'thdn_anomaly_count': int,           # THDN异常段数
    'scaling_anomaly_count': int,       # 缩放系数异常段数
    'is_thdn_anomaly': bool,            # 是否THDN异常
    'is_scaling_anomaly': bool,        # 是否缩放系数异常
    'thdn_threshold': int,              # 使用的THDN阈值
    'scaling_threshold': int,          # 使用的缩放系数阈值
    'file_name': str,                  # 文件名
    'detection_time': str,             # 检测时间
    'thdn_anomaly_segments': List,      # THDN异常段详情
    'scaling_anomaly_segments': List   # 缩放系数异常段详情
}
```

**使用示例**:
```python
from anomaly_detector import detect_audio_anomaly_detailed

result = detect_audio_anomaly_detailed("test.wav")
print(f"结果: {result['anomaly_level']}")
print(f"THDN异常段数: {result['thdn_anomaly_count']}")
print(f"缩放系数异常段数: {result['scaling_anomaly_count']}")
```

## 异常判定规则

### 段级异常判定
1. **THDN异常**: 当某段THDN值 > 该段基准最大值时判定为异常
2. **缩放系数异常**: 当某段缩放系数偏离基准范围的绝对偏差 > 1.0时判定为异常

### 文件级异常判定
- **THDN异常**: THDN异常段数 > thdn_threshold (默认5段)
- **缩放系数异常**: 缩放系数异常段数 > scaling_threshold (默认2段)
- **最终判定**: THDN异常 OR 缩放系数异常 → 文件异常

## 批量检测API

批量检测脚本已重构为使用API：

```python
from batch_detection import batch_detect_files

summary = batch_detect_files(
    test_folders=["../test20250722"],
    output_dir="detection_results",
    thdn_threshold=5,
    scaling_threshold=2,
    baseline_file="baseline_standard.json",
    worker_num=20
)
```

### 输出文件命名规则

批量检测会生成带时间戳的输出文件，避免文件覆盖：

- **汇总文件**: `detection_summary_YYYYMMDD_HHMMSS.json`
- **详细结果**: `all_detection_results_YYYYMMDD_HHMMSS.json`
- **分文件夹结果**: `{folder_name}_results_YYYYMMDD_HHMMSS.json`

**示例文件名**:
- `detection_summary_20250730_151029.json`
- `neg_results_20250730_151029.json`
- `test20250722_results_20250730_151029.json`

## 实际应用示例

### 示例1: 单文件检测
```python
from anomaly_detector import detect_audio_anomaly

# 检测单个文件
audio_file = "audio_sample.wav"
result = detect_audio_anomaly(audio_file, verbose=True)

if result == "异常":
    print("⚠️ 发现音频质量问题，需要人工检查")
else:
    print("✅ 音频质量正常")
```

### 示例2: 批量文件检测
```python
import os
from anomaly_detector import detect_audio_anomaly

folder_path = "audio_samples/"
normal_count = 0
anomaly_count = 0

for filename in os.listdir(folder_path):
    if filename.endswith('.wav'):
        file_path = os.path.join(folder_path, filename)
        result = detect_audio_anomaly(file_path)
        
        if result == "正常":
            normal_count += 1
        else:
            anomaly_count += 1
            print(f"异常文件: {filename}")

print(f"检测完成: {normal_count}个正常, {anomaly_count}个异常")
```

### 示例3: 自定义阈值检测
```python
from anomaly_detector import detect_audio_anomaly_detailed

# 严格模式 (更敏感)
strict_result = detect_audio_anomaly_detailed(
    "test.wav", 
    thdn_threshold=3, 
    scaling_threshold=1
)

# 宽松模式 (更宽容)
loose_result = detect_audio_anomaly_detailed(
    "test.wav", 
    thdn_threshold=8, 
    scaling_threshold=3
)

print(f"严格模式: {strict_result['anomaly_level']}")
print(f"宽松模式: {loose_result['anomaly_level']}")
```

## 错误处理

API具有完善的错误处理机制：

1. **基准文件不存在**: 返回"异常"并提示错误信息
2. **音频文件无法分析**: 返回"异常"并记录错误
3. **其他异常**: 捕获所有异常，保守返回"异常"

```python
result = detect_audio_anomaly_detailed("nonexistent.wav")
if 'error' in result:
    print(f"检测失败: {result['error']}")
```

## 性能优化建议

1. **并行处理**: 调整`worker_num`参数优化性能
2. **批量检测**: 使用批量检测函数而非循环调用单文件API
3. **阈值调整**: 根据实际需求调整阈值以平衡精度和性能

## 集成建议

### Web服务集成
```python
from flask import Flask, request, jsonify
from anomaly_detector import detect_audio_anomaly

app = Flask(__name__)

@app.route('/detect', methods=['POST'])
def detect_audio():
    audio_file = request.files['audio']
    # 保存文件并检测
    result = detect_audio_anomaly(audio_file.filename)
    return jsonify({'result': result})
```

### 生产环境部署
1. **基准文件管理**: 确保baseline_standard.json文件可访问
2. **资源控制**: 根据服务器性能调整worker_num
3. **日志记录**: 启用verbose模式记录详细日志
4. **监控告警**: 监控异常率变化趋势

## 版本信息

- **API版本**: 1.0
- **基准数据版本**: 基于40个高质量pos文件
- **支持格式**: WAV音频文件
- **分析频段**: 93段 (100Hz-20kHz)

API已经过充分测试，可用于生产环境的音频质量检测。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
THDN异常检测器 - 使用预建立的基准标准
"""

import os
import sys
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional
import time

# 添加父目录到路径以导入thdn_analyzer
sys.path.append('..')
from thdn_analyzer import analyze_audio_thdn

class THDNAnomalyDetector:
    """THDN异常检测器"""

    def __init__(self, baseline_file: str = "baseline_standard.json"):
        """
        初始化异常检测器

        Args:
            baseline_file: 基准标准文件路径
        """
        self.baseline_standard = None
        self.baseline_file = baseline_file

        # 自动加载基准标准
        if os.path.exists(baseline_file):
            self.load_baseline_standard(baseline_file)
        else:
            print(f"⚠️ 基准标准文件不存在: {baseline_file}")
            print(f"请先运行 build_baseline_standard.py 建立基准标准")
    
    def load_baseline_standard(self, baseline_file: str) -> bool:
        """
        加载基准标准文件
        
        Args:
            baseline_file: 基准标准文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            with open(baseline_file, 'r', encoding='utf-8') as f:
                self.baseline_standard = json.load(f)
            
            print(f"✅ 基准标准已加载: {baseline_file}")            
            return True
            
        except Exception as e:
            print(f"❌ 加载基准标准失败: {str(e)}")
            return False
    
    def detect_single_file(self, test_file: str, worker_num: int = 4, thdn_threshold: int = 2, amplitude_threshold: int = 2) -> Optional[Dict]:
        """
        检测单个文件的异常

        Args:
            test_file: 待检测文件路径
            worker_num: 并行处理进程数
            thdn_threshold: THDN dB异常判定段数阈值
            amplitude_threshold: 主频幅值dB异常判定段数阈值

        Returns:
            Dict: 异常检测结果，None表示检测失败
        """
        if self.baseline_standard is None:
            print("❌ 尚未加载基准标准")
            return None
        
        print(f"🔍 检测文件: {Path(test_file).name}")
        
        try:
            # 分析文件
            success, results = analyze_audio_thdn(test_file, worker_num=worker_num)
            
            if not success or not results or len(results) != 93:
                print(f"❌ 文件分析失败或段数不正确 (期望93段，实际{len(results) if results else 0}段)")
                return None
            
            # 提取THDN百分比和主频幅值dB值
            thdn_values = np.array([r['thdn'] for r in results])
            amplitude_db_values = np.array([r['test_peak_amp_db'] for r in results])

            # 获取基准范围（THDN用百分比，幅值用dB）
            thdn_max = np.array(self.baseline_standard['thdn_statistics']['max_per_segment'])
            amplitude_db_min = np.array(self.baseline_standard['amplitude_db_statistics']['min_per_segment'])
            amplitude_db_max = np.array(self.baseline_standard['amplitude_db_statistics']['max_per_segment'])

            # 检测异常（THDN用百分比，幅值用dB）
            # THDN百分比异常：大于基准最大值
            thdn_anomalies = (thdn_values > thdn_max)

            # 高THDN异常：THDN大于10%的段
            high_thdn_anomalies = (thdn_values > 10.0)

            # 主频幅值dB异常：新的判定规则
            # 1. 大于基准最大值+6dB（2倍）
            # 2. 小于基准最小值-6dB（0.5倍）
            # 3. 小于-60dB（极小值）
            amplitude_anomalies = (
                (amplitude_db_values > amplitude_db_max + 6.0) |
                (amplitude_db_values < amplitude_db_min - 6.0) |
                (amplitude_db_values < -60.0)
            )
            
            # 统计异常数量
            thdn_anomaly_count = int(np.sum(thdn_anomalies))
            high_thdn_anomaly_count = int(np.sum(high_thdn_anomalies))
            amplitude_anomaly_count = int(np.sum(amplitude_anomalies))
            
            # 找出异常段的详细信息
            thdn_anomaly_segments = []
            amplitude_anomaly_segments = []

            for i in range(93):
                if thdn_anomalies[i]:
                    thdn_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value_percent': float(thdn_values[i]),
                        'baseline_max_percent': float(thdn_max[i]),
                        'deviation_type': 'above',
                        'deviation_amount_percent': float(thdn_values[i] - thdn_max[i])
                    })

                if amplitude_anomalies[i]:
                    # 判断异常类型和偏差量（dB值）
                    current_value_db = amplitude_db_values[i]
                    min_val_db = amplitude_db_min[i]
                    max_val_db = amplitude_db_max[i]

                    if current_value_db < -60.0:
                        deviation_type = 'too_small'
                        deviation_amount_db = -60.0 - current_value_db
                    elif current_value_db < min_val_db - 6.0:
                        deviation_type = 'below_threshold'
                        deviation_amount_db = (min_val_db - 6.0) - current_value_db
                    elif current_value_db > max_val_db + 6.0:
                        deviation_type = 'above_threshold'
                        deviation_amount_db = current_value_db - (max_val_db + 6.0)
                    else:
                        deviation_type = 'unknown'
                        deviation_amount_db = 0.0

                    amplitude_anomaly_segments.append({
                        'segment': i + 1,
                        'frequency': results[i]['expected_freq'],
                        'value_db': float(current_value_db),
                        'baseline_min_db': float(min_val_db),
                        'baseline_max_db': float(max_val_db),
                        'deviation_type': deviation_type,
                        'deviation_amount_db': float(deviation_amount_db),
                        'threshold_min_db': float(min_val_db - 6.0),
                        'threshold_max_db': float(max_val_db + 6.0),
                        'absolute_min_db': -60.0
                    })

            # 判断整体异常状态
            # 1. THDN超过thdn_threshold段
            # 2. 主频幅值超过amplitude_threshold段
            # 3. 有任何1段THDN大于10%
            is_thdn_anomaly = thdn_anomaly_count > thdn_threshold
            is_amplitude_anomaly = amplitude_anomaly_count > amplitude_threshold
            is_high_thdn_anomaly = high_thdn_anomaly_count > 0

            is_anomaly = is_thdn_anomaly or is_amplitude_anomaly or is_high_thdn_anomaly
            anomaly_level = "异常" if is_anomaly else "正常"
            
            # 构建结果
            result = {
                'file_info': {
                    'name': Path(test_file).name,
                    'path': test_file,
                    'analysis_time': time.strftime("%Y-%m-%d %H:%M:%S")
                },
                'anomaly_summary': {
                    'thdn_anomaly_count': thdn_anomaly_count,
                    'high_thdn_anomaly_count': high_thdn_anomaly_count,
                    'amplitude_anomaly_count': amplitude_anomaly_count,
                    'is_thdn_anomaly': is_thdn_anomaly,
                    'is_high_thdn_anomaly': is_high_thdn_anomaly,
                    'is_amplitude_anomaly': is_amplitude_anomaly,
                    'thdn_threshold': thdn_threshold,
                    'amplitude_threshold': amplitude_threshold,
                    'anomaly_level': anomaly_level
                },
                'anomaly_details': {
                    'thdn_anomaly_segments': thdn_anomaly_segments,
                    'amplitude_anomaly_segments': amplitude_anomaly_segments
                },
                'raw_data': {
                    'thdn_values': thdn_values.tolist(),
                    'amplitude_db_values': amplitude_db_values.tolist()
                },
                'baseline_info': {
                    'baseline_file': self.baseline_file,
                }
            }
            
            print(f"✅ 检测完成:")
            print(f"  THDN异常段数: {thdn_anomaly_count}/93 ({'异常' if is_thdn_anomaly else '正常'})")
            print(f"  高THDN异常段数(>10%): {high_thdn_anomaly_count}/93 ({'异常' if is_high_thdn_anomaly else '正常'})")
            print(f"  主频幅值异常段数: {amplitude_anomaly_count}/93 ({'异常' if is_amplitude_anomaly else '正常'})")
            print(f"  最终判定: {anomaly_level}")
            
            return result
            
        except Exception as e:
            print(f"❌ 异常检测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def detect_batch_files(self, file_list: List[str], worker_num: int = 4) -> List[Dict]:
        """
        批量检测多个文件
        
        Args:
            file_list: 待检测文件列表
            worker_num: 并行处理进程数
            
        Returns:
            List[Dict]: 检测结果列表
        """
        print(f"🚀 批量检测 {len(file_list)} 个文件")
        print("=" * 50)
        
        results = []
        
        for i, file_path in enumerate(file_list, 1):
            print(f"\n🎵 检测文件 {i}/{len(file_list)}: {Path(file_path).name}")
            
            result = self.detect_single_file(file_path, worker_num)
            
            if result:
                results.append(result)
                level = result['anomaly_summary']['anomaly_level']
                thdn_count = result['anomaly_summary']['thdn_anomaly_count']
                scaling_count = result['anomaly_summary']['scaling_anomaly_count']
                print(f"  ✅ {level} - THDN异常:{thdn_count}, 缩放异常:{scaling_count}")
            else:
                print(f"  ❌ 检测失败")
        
        return results
    
    def save_results(self, results: List[Dict], output_file: str) -> bool:
        """
        保存检测结果到文件
        
        Args:
            results: 检测结果列表
            output_file: 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 添加汇总信息
            summary = {
                'detection_summary': {
                    'total_files': len(results),
                    'detection_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'baseline_info': results[0]['baseline_info'] if results else None
                },
                'level_statistics': {
                    'normal': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "正常"),
                    'mild': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "轻微"),
                    'moderate': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "中等"),
                    'severe': sum(1 for r in results if r['anomaly_summary']['anomaly_level'] == "严重")
                },
                'detailed_results': results
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 检测结果已保存: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")
            return False

# API封装函数
def detect_audio_anomaly(audio_path: str,
                         thdn_threshold: int = 5,
                         scaling_threshold: int = 2,
                         baseline_file: str = "baseline_standard.json",
                         worker_num: int = 4,
                         verbose: bool = False) -> str:
    """
    THDN异常检测API

    Args:
        audio_path: 音频文件路径
        thdn_threshold: THDN异常判定段数阈值，默认5段
        scaling_threshold: 缩放系数异常判定段数阈值，默认2段
        baseline_file: 基准标准文件路径
        worker_num: 并行处理进程数
        verbose: 是否输出详细信息

    Returns:
        str: "正常" 或 "异常"
    """
    try:
        # 创建检测器
        detector = THDNAnomalyDetector(baseline_file)

        if detector.baseline_standard is None:
            if verbose:
                print(f"❌ 无法加载基准标准: {baseline_file}")
            return "异常"  # 无法检测时保守返回异常

        # 检测文件
        result = detector.detect_single_file(audio_path, worker_num=worker_num,
                                           thdn_threshold=thdn_threshold,
                                           scaling_threshold=scaling_threshold)

        if result is None:
            if verbose:
                print(f"❌ 文件检测失败: {audio_path}")
            return "异常"  # 检测失败时保守返回异常

        # 使用检测结果中的判定（已包含所有规则）
        anomaly_level = result['anomaly_summary']['anomaly_level']

        if verbose:
            print(f" 文件: {Path(audio_path).name}")
            print(f"  THDN异常段数: {result['anomaly_summary']['thdn_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_thdn_anomaly'] else '正常'})")
            print(f"  高THDN异常段数(>10%): {result['anomaly_summary']['high_thdn_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_high_thdn_anomaly'] else '正常'})")
            print(f"  缩放系数异常段数: {result['anomaly_summary']['scaling_anomaly_count']}/{93} ({'异常' if result['anomaly_summary']['is_scaling_anomaly'] else '正常'})")
            print(f"  最终判定: {anomaly_level}")

        return anomaly_level

    except Exception as e:
        if verbose:
            print(f"❌ API调用异常: {str(e)}")
        return "异常"  # 异常时保守返回异常

def detect_audio_anomaly_detailed(audio_path: str,
                                 thdn_threshold: int = 5,
                                 scaling_threshold: int = 2,
                                 baseline_file: str = "baseline_standard.json",
                                 worker_num: int = 4) -> Dict:
    """
    THDN异常检测API（详细版本）

    Args:
        audio_path: 音频文件路径
        thdn_threshold: THDN异常判定段数阈值，默认5段
        scaling_threshold: 缩放系数异常判定段数阈值，默认2段
        baseline_file: 基准标准文件路径
        worker_num: 并行处理进程数

    Returns:
        Dict: 详细检测结果，包含异常段信息等
    """
    try:
        # 创建检测器
        detector = THDNAnomalyDetector(baseline_file)

        if detector.baseline_standard is None:
            return {
                'anomaly_level': '异常',
                'error': f'无法加载基准标准: {baseline_file}',
                'thdn_anomaly_count': 0,
                'scaling_anomaly_count': 0
            }

        # 检测文件
        result = detector.detect_single_file(audio_path, worker_num=worker_num,
                                           thdn_threshold=thdn_threshold,
                                           scaling_threshold=scaling_threshold)

        if result is None:
            return {
                'anomaly_level': '异常',
                'error': f'文件检测失败: {audio_path}',
                'thdn_anomaly_count': 0,
                'scaling_anomaly_count': 0
            }

        # 直接使用检测结果（已包含所有规则和阈值）
        api_result = {
            'anomaly_level': result['anomaly_summary']['anomaly_level'],
            'thdn_anomaly_count': result['anomaly_summary']['thdn_anomaly_count'],
            'high_thdn_anomaly_count': result['anomaly_summary']['high_thdn_anomaly_count'],
            'scaling_anomaly_count': result['anomaly_summary']['scaling_anomaly_count'],
            'is_thdn_anomaly': result['anomaly_summary']['is_thdn_anomaly'],
            'is_high_thdn_anomaly': result['anomaly_summary']['is_high_thdn_anomaly'],
            'is_scaling_anomaly': result['anomaly_summary']['is_scaling_anomaly'],
            'thdn_threshold': result['anomaly_summary']['thdn_threshold'],
            'scaling_threshold': result['anomaly_summary']['scaling_threshold'],
            'file_name': Path(audio_path).name,
            'detection_time': result['file_info']['analysis_time'],
            'thdn_anomaly_segments': result['anomaly_details']['thdn_anomaly_segments'],
            'scaling_anomaly_segments': result['anomaly_details']['scaling_anomaly_segments']
        }

        return api_result

    except Exception as e:
        return {
            'anomaly_level': '异常',
            'error': f'API调用异常: {str(e)}',
            'thdn_anomaly_count': 0,
            'scaling_anomaly_count': 0
        }


if __name__ == "__main__":
    """主函数 - API使用示例"""
    print("THDN异常检测API示例")
    print("=" * 50)

    # 示例文件
    test_file = "../test20250722/鼓膜破裂（复测1.1).wav"

    if not Path(test_file).exists():
        print(f"⚠️ 示例文件不存在: {test_file}")
        raise FileNotFoundError(f"示例文件不存在: {test_file}")

    print(f"\n🧪 API使用示例")
    print(f"测试文件: {Path(test_file).name}")

    # 示例1: 简单API调用
    print(f"\n1️⃣ 简单API调用 (默认阈值)")
    result = detect_audio_anomaly(test_file, verbose=True)
    print(f"结果: {result}")

    # 示例2: 自定义阈值
    print(f"\n2️⃣ 自定义阈值API调用 (THDN阈值=3, 缩放阈值=1)")
    result = detect_audio_anomaly(test_file, thdn_threshold=3, scaling_threshold=1, verbose=True)
    print(f"结果: {result}")

    # 示例3: 详细结果API
    print(f"\n3️⃣ 详细结果API调用")
    detailed_result = detect_audio_anomaly_detailed(test_file)
    print(f"结果: {detailed_result['anomaly_level']}")
    print(f"THDN异常段数: {detailed_result['thdn_anomaly_count']}")
    print(f"高THDN异常段数(>10%): {detailed_result['high_thdn_anomaly_count']}")
    print(f"缩放系数异常段数: {detailed_result['scaling_anomaly_count']}")
    if detailed_result['thdn_anomaly_count'] > 0:
        print(f"THDN异常段示例: {detailed_result['thdn_anomaly_segments'][:2]}")

